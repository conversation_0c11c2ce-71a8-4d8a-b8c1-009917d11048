function analyze_filter_response(csv_filename)
% ANALYZE_FILTER_RESPONSE 分析滤波器频率响应数据
% 
% 输入参数:
%   csv_filename - CSV数据文件名（可选，默认会弹出文件选择对话框）
%
% 使用示例:
%   analyze_filter_response('frequency_sweep_20241201_143022.csv')
%   analyze_filter_response()  % 弹出文件选择对话框

    % 如果没有提供文件名，弹出文件选择对话框
    if nargin < 1
        [filename, pathname] = uigetfile('*.csv', '选择频率扫描数据文件');
        if isequal(filename, 0)
            disp('用户取消了文件选择');
            return;
        end
        csv_filename = fullfile(pathname, filename);
    end
    
    % 检查文件是否存在
    if ~exist(csv_filename, 'file')
        error('文件不存在: %s', csv_filename);
    end
    
    % 读取CSV数据
    fprintf('正在读取数据文件: %s\n', csv_filename);
    try
        data = readtable(csv_filename);
        fprintf('成功读取 %d 行数据\n', height(data));
    catch ME
        error('读取CSV文件失败: %s', ME.message);
    end
    
    % 提取数据列
    frequency = data.frequency;
    amplitude_ratio = data.amplitude_ratio;
    
    % 数据验证
    if length(frequency) < 10
        warning('数据点太少，可能影响分析结果');
    end
    
    % 计算幅度响应（dB）
    amplitude_db = 20 * log10(amplitude_ratio);
    
    % 创建图形窗口
    figure('Name', '滤波器频率响应分析', 'Position', [100, 100, 1200, 600]);

    % 子图1: 幅频特性 (dB)
    subplot(1, 2, 1);
    semilogx(frequency, amplitude_db, 'b-', 'LineWidth', 2);
    grid on;
    xlabel('频率 (Hz)');
    ylabel('幅度 (dB)');
    title('幅频特性 (dB)');

    % 标记-3dB点
    max_gain = max(amplitude_db);
    cutoff_level = max_gain - 3;
    hold on;
    plot([min(frequency), max(frequency)], [cutoff_level, cutoff_level], 'r--', 'LineWidth', 1);

    % 找到截止频率
    cutoff_indices = find(amplitude_db >= cutoff_level);
    if ~isempty(cutoff_indices)
        cutoff_freq = frequency(cutoff_indices(end));
        plot(cutoff_freq, cutoff_level, 'ro', 'MarkerSize', 8, 'MarkerFaceColor', 'r');
        text(cutoff_freq, cutoff_level + 1, sprintf('f_c ≈ %.0f Hz', cutoff_freq), ...
             'HorizontalAlignment', 'center');
        fprintf('估计截止频率 (-3dB): %.0f Hz\n', cutoff_freq);
    end
    hold off;

    % 子图2: 幅度比（线性刻度）
    subplot(1, 2, 2);
    semilogx(frequency, amplitude_ratio, 'm-', 'LineWidth', 2);
    grid on;
    xlabel('频率 (Hz)');
    ylabel('幅度比');
    title('传递函数幅度 (线性)');

    % 标记0.707点（-3dB）
    hold on;
    plot([min(frequency), max(frequency)], [0.707, 0.707], 'r--', 'LineWidth', 1);
    if exist('cutoff_freq', 'var')
        cutoff_ratio = interp1(frequency, amplitude_ratio, cutoff_freq);
        plot(cutoff_freq, cutoff_ratio, 'ro', 'MarkerSize', 8, 'MarkerFaceColor', 'r');
    end
    hold off;
    
    % 调整子图间距
    sgtitle(sprintf('滤波器频率响应分析 - %s', csv_filename), 'Interpreter', 'none');
    
    % 分析统计信息
    fprintf('\n=== 滤波器特性分析 ===\n');
    fprintf('频率范围: %.0f Hz - %.0f Hz\n', min(frequency), max(frequency));
    fprintf('数据点数: %d\n', length(frequency));
    fprintf('通带增益: %.2f dB\n', max_gain);
    
    % 计算滚降率（如果是低通滤波器）
    if exist('cutoff_freq', 'var') && cutoff_freq < max(frequency)
        % 找到截止频率后的数据点
        high_freq_indices = find(frequency > cutoff_freq * 2);
        if length(high_freq_indices) > 5
            % 在高频段拟合直线计算滚降率
            high_freq = frequency(high_freq_indices);
            high_gain = amplitude_db(high_freq_indices);
            
            % 线性拟合 (log频率 vs dB)
            p = polyfit(log10(high_freq), high_gain, 1);
            rolloff_rate = p(1) * 20; % dB/decade
            
            fprintf('滚降率: %.1f dB/decade\n', rolloff_rate);
            
            % 估计滤波器阶数
            if abs(rolloff_rate + 20) < 5
                fprintf('估计滤波器阶数: 1阶\n');
            elseif abs(rolloff_rate + 40) < 10
                fprintf('估计滤波器阶数: 2阶\n');
            elseif abs(rolloff_rate + 60) < 15
                fprintf('估计滤波器阶数: 3阶\n');
            else
                fprintf('估计滤波器阶数: %.1f阶\n', abs(rolloff_rate) / 20);
            end
        end
    end
    

    
    % 保存分析结果
    [~, name, ~] = fileparts(csv_filename);
    result_filename = [name, '_analysis_results.mat'];
    save(result_filename, 'frequency', 'amplitude_db', 'amplitude_ratio');
    fprintf('\n分析结果已保存到: %s\n', result_filename);
    
    fprintf('\n分析完成！\n');
end

% 如果直接运行此脚本，调用主函数
if ~exist('csv_filename', 'var')
    analyze_filter_response();
end
