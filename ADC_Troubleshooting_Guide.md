# ADC采样问题诊断指南

## 问题描述
ADC1和ADC2无法同时正常采样，可能的原因包括：
1. ADC配置问题
2. 同步触发问题
3. 中断配置问题
4. 时钟配置问题

## 已实施的修复

### 1. ADC模式修改
**问题：** 原来使用双重同步模式但DMA被禁用
**修复：** 改为独立模式，通过外部触发器同步

```c
// 修改前：双重同步模式
ADC_CommonInitStructure.ADC_Mode = ADC_DualMode_RegSimult;

// 修改后：独立模式
ADC_CommonInitStructure.ADC_Mode = ADC_Mode_Independent;
```

### 2. 添加诊断功能
- `ADC_Diagnostics()` - 完整的ADC状态检查
- `Print_ADC_Status()` - 简化的状态打印
- 扫频开始时自动打印诊断信息

## 诊断步骤

### 步骤1：检查基本配置
运行扫频测试，观察串口输出的诊断信息：

```
=== ADC诊断信息 ===
ADC1 使能状态: 是
ADC2 使能状态: 是
ADC1 EOC中断: 使能
ADC2 EOC中断: 使能
ADC1 外部触发: 0x01000000
ADC2 外部触发: 0x01000000
TIM3 使能状态: 是
TIM3 计数值: xxx
TIM3 ARR值: 102
ADC1 采样索引: 0, 完成标志: 0
ADC2 采样索引: 0, 完成标志: 0
===================
```

### 步骤2：检查关键指标

**正常状态应该显示：**
- ADC1和ADC2都使能
- EOC中断都使能
- 外部触发配置相同（0x01000000 = TIM3_TRGO + 上升沿）
- TIM3正在运行（计数值在变化）
- TIM3 ARR值为102（对应815kHz采样率）

**异常情况：**
- 如果ADC未使能 → 检查ADC初始化
- 如果中断未使能 → 检查中断配置
- 如果TIM3未运行 → 检查定时器配置
- 如果外部触发不匹配 → 检查触发源配置

### 步骤3：监控采样进度
观察状态打印：
```
ADC状态: ADC1[0/4096] ADC2[0/4096] 完成[0,0]
ADC状态: ADC1[1024/4096] ADC2[1024/4096] 完成[0,0]
ADC状态: ADC1[4096/4096] ADC2[4096/4096] 完成[1,1]
```

**正常情况：**
- ADC1和ADC2的采样索引应该同步增长
- 达到4096时完成标志变为1

**异常情况：**
- 如果索引不增长 → TIM3触发或ADC中断问题
- 如果只有一个ADC在采样 → 检查另一个ADC的配置
- 如果采样速度异常 → 检查TIM3频率配置

## 常见问题及解决方案

### 问题1：ADC采样索引不增长
**可能原因：**
- TIM3未正确触发ADC
- ADC中断未正确配置
- NVIC优先级冲突

**解决方案：**
1. 检查TIM3的TRGO配置
2. 检查ADC的外部触发配置
3. 检查NVIC中断优先级

### 问题2：只有一个ADC在采样
**可能原因：**
- ADC2初始化问题
- 中断服务函数问题
- 引脚配置问题

**解决方案：**
1. 检查ADC2的GPIO配置（PC1）
2. 检查ADC2的时钟使能
3. 检查中断服务函数中的ADC2处理

### 问题3：采样速度异常
**可能原因：**
- TIM3频率配置错误
- ADC转换时间过长
- 中断处理时间过长

**解决方案：**
1. 验证TIM3频率：84MHz / 103 / 1 = 815534Hz
2. 检查ADC采样时间配置（3个周期）
3. 优化中断服务函数

### 问题4：数据同步问题
**可能原因：**
- ADC1和ADC2触发时序不同步
- 中断处理顺序问题

**解决方案：**
1. 确保两个ADC使用相同的外部触发源
2. 在中断服务函数中正确处理两个ADC
3. 检查采样缓冲区的索引同步

## 进一步调试

### 使用示波器检查
1. **TIM3输出：** 检查TRGO信号是否正常
2. **ADC输入：** 检查PA1和PC1的信号
3. **时钟信号：** 检查ADC时钟是否正常

### 代码级调试
1. 在ADC中断服务函数中添加断点
2. 检查ADC寄存器状态
3. 验证采样数据的合理性

### 性能测试
1. 测量实际采样率
2. 检查CPU占用率
3. 验证内存使用情况

## 预期结果
修复后应该看到：
1. 两个ADC同步采样
2. 采样索引同步增长
3. 4096个采样点后完成标志置位
4. 正确的幅度比值输出

## 联系支持
如果问题仍然存在，请提供：
1. 完整的诊断输出
2. 示波器截图（如果可用）
3. 具体的错误现象描述
