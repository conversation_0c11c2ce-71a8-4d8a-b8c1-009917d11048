#!/usr/bin/env python3
"""
串口数据接收和分析脚本
用于接收STM32发送的频率扫描数据并进行实时分析
"""

import serial
import csv
import time
import matplotlib.pyplot as plt
import numpy as np
from datetime import datetime

class FrequencySweepAnalyzer:
    def __init__(self, port='COM3', baudrate=115200):
        """
        初始化串口连接和数据存储
        
        Args:
            port: 串口号
            baudrate: 波特率
        """
        self.port = port
        self.baudrate = baudrate
        self.serial_conn = None
        self.data = []
        self.frequencies = []
        self.amplitude_ratios = []
        
    def connect(self):
        """连接串口"""
        try:
            self.serial_conn = serial.Serial(self.port, self.baudrate, timeout=1)
            print(f"已连接到 {self.port}，波特率 {self.baudrate}")
            return True
        except Exception as e:
            print(f"串口连接失败: {e}")
            return False
    
    def disconnect(self):
        """断开串口连接"""
        if self.serial_conn and self.serial_conn.is_open:
            self.serial_conn.close()
            print("串口已断开")
    
    def read_data(self, timeout=3600):
        """
        读取串口数据
        
        Args:
            timeout: 超时时间（秒）
        """
        if not self.serial_conn or not self.serial_conn.is_open:
            print("串口未连接")
            return False
        
        print("开始接收数据...")
        start_time = time.time()
        data_started = False
        
        while time.time() - start_time < timeout:
            try:
                line = self.serial_conn.readline().decode('utf-8').strip()
                
                if line:
                    print(f"接收: {line}")
                    
                    # 检查是否是CSV表头
                    if "Frequency(Hz)" in line:
                        data_started = True
                        print("检测到数据表头，开始记录数据")
                        continue
                    
                    # 检查是否是扫频完成标志
                    if "SWEEP_COMPLETED" in line:
                        print("扫频完成")
                        break
                    
                    # 解析数据行
                    if data_started and ',' in line:
                        try:
                            parts = line.split(',')
                            if len(parts) >= 2:
                                freq = float(parts[0])
                                ratio = float(parts[1])

                                self.frequencies.append(freq)
                                self.amplitude_ratios.append(ratio)

                                self.data.append({
                                    'frequency': freq,
                                    'amplitude_ratio': ratio
                                })

                                print(f"数据点 {len(self.data)}: {freq:.0f}Hz, 比值={ratio:.6f}")
                        
                        except ValueError as e:
                            print(f"数据解析错误: {line} - {e}")
            
            except Exception as e:
                print(f"读取数据错误: {e}")
                break
        
        print(f"数据接收完成，共收到 {len(self.data)} 个数据点")
        return len(self.data) > 0
    
    def save_data(self, filename=None):
        """保存数据到CSV文件"""
        if not self.data:
            print("没有数据可保存")
            return False
        
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"frequency_sweep_{timestamp}.csv"
        
        try:
            with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
                fieldnames = ['frequency', 'amplitude_ratio']
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                
                writer.writeheader()
                for row in self.data:
                    writer.writerow(row)
            
            print(f"数据已保存到 {filename}")
            return True
        
        except Exception as e:
            print(f"保存数据失败: {e}")
            return False
    
    def plot_results(self):
        """绘制分析结果"""
        if not self.data:
            print("没有数据可绘制")
            return

        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 8))

        # 幅频特性 (dB)
        ax1.semilogx(self.frequencies, 20*np.log10(np.array(self.amplitude_ratios)))
        ax1.set_xlabel('频率 (Hz)')
        ax1.set_ylabel('幅度 (dB)')
        ax1.set_title('滤波器幅频特性 (dB)')
        ax1.grid(True)

        # 幅频特性 (线性)
        ax2.semilogx(self.frequencies, self.amplitude_ratios)
        ax2.set_xlabel('频率 (Hz)')
        ax2.set_ylabel('幅度比')
        ax2.set_title('滤波器幅频特性 (线性)')
        ax2.grid(True)

        plt.tight_layout()
        plt.show()
    
    def analyze_filter(self):
        """分析滤波器特性"""
        if not self.data:
            print("没有数据可分析")
            return
        
        ratios = np.array(self.amplitude_ratios)
        freqs = np.array(self.frequencies)
        
        # 找到-3dB点
        ratios_db = 20 * np.log10(ratios)
        max_gain = np.max(ratios_db)
        cutoff_level = max_gain - 3
        
        # 找到最接近-3dB的点
        cutoff_indices = np.where(ratios_db >= cutoff_level)[0]
        if len(cutoff_indices) > 0:
            cutoff_freq = freqs[cutoff_indices[-1]]
            print(f"估计截止频率 (-3dB): {cutoff_freq:.0f} Hz")
        
        # 通带增益
        passband_gain = max_gain
        print(f"通带增益: {passband_gain:.2f} dB")
        
        # 频率范围
        print(f"测试频率范围: {freqs[0]:.0f} Hz - {freqs[-1]:.0f} Hz")
        print(f"数据点数: {len(self.data)}")

def main():
    """主函数"""
    print("STM32频率扫描数据分析工具")
    print("=" * 40)
    
    # 创建分析器实例
    analyzer = FrequencySweepAnalyzer(port='COM3', baudrate=115200)
    
    # 连接串口
    if not analyzer.connect():
        print("无法连接串口，请检查端口号和设备连接")
        return
    
    try:
        # 读取数据
        if analyzer.read_data(timeout=3600):  # 1小时超时
            # 保存数据
            analyzer.save_data()
            
            # 分析滤波器特性
            analyzer.analyze_filter()
            
            # 绘制结果
            analyzer.plot_results()
        else:
            print("未接收到有效数据")
    
    except KeyboardInterrupt:
        print("\n用户中断，正在保存已接收的数据...")
        if analyzer.data:
            analyzer.save_data()
            analyzer.analyze_filter()
    
    finally:
        # 断开连接
        analyzer.disconnect()

if __name__ == "__main__":
    main()
