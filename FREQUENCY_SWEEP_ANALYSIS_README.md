# 频率扫描滤波器特性分析系统

## 系统概述

本系统使用STM32F4微控制器实现滤波器的幅频特性和相频特性分析。通过AD9833产生扫频信号，使用双ADC同步采样，并通过FFT分析得到滤波器的频率响应特性。

## 硬件连接

### 信号连接
- **AD9833输出**: 扫频信号源，连接到被测滤波器输入
- **ADC1 (PA1)**: 采样滤波器输出信号（滤波后信号）
- **ADC2 (PC1)**: 采样滤波器输入信号（原始扫频信号）

### 控制接口
- **PE4 (KEY0)**: 选择按钮，用于切换功能选项
- **PE3 (KEY1)**: 确认按钮，用于执行选中的功能
- **串口**: 115200波特率，输出分析结果

## 功能特性

### 扫频参数
- **起始频率**: 1 kHz
- **结束频率**: 400.2 kHz  
- **频率步进**: 200 Hz
- **总步数**: 约2000个频率点
- **采样率**: 815.534 kHz
- **FFT点数**: 4096点/频率

### 分析功能
1. **幅频特性分析**: 计算滤波器的幅度传递函数
2. **实时显示**: LCD显示当前分析状态
3. **数据输出**: 串口输出简化的CSV格式数据（频率和幅度比值）

## 操作说明

### 启动分析
1. 连接硬件，确保信号连接正确
2. 上电启动系统
3. 使用PE4按钮选择"ADC OFF"按钮
4. 按PE3确认，启动扫频分析

### 数据输出格式
串口输出简化的CSV格式数据：
```
Frequency(Hz),Amplitude_Ratio
1000,0.800000
1200,0.791234
1400,0.782456
...
```

### 数据说明
- **Frequency(Hz)**: 测试频率点
- **Amplitude_Ratio**: 幅度比 (滤波器输出/输入)

## 技术参数

### ADC配置
- **分辨率**: 12位
- **参考电压**: 3.3V
- **采样模式**: 定时器触发同步采样
- **触发频率**: 815.534 kHz

### FFT分析
- **算法**: ARM CMSIS DSP库 Radix-4 FFT
- **窗函数**: 汉宁窗（可选）
- **频率分辨率**: 约199 Hz (815534/4096)
- **相位精度**: 约0.1度

### 扫频控制
- **扫频速度**: 约2秒/频率点（包含采样和分析时间）
- **频率精度**: 由AD9833决定，约0.1 Hz
- **扫频范围**: 可配置，当前设置1kHz-400kHz

## 数据处理建议

### MATLAB/Python处理示例
```matlab
% 读取CSV数据
data = readtable('sweep_results.csv');
freq = data.Frequency_Hz_;
amplitude_ratio = data.Amplitude_Ratio;
phase_diff = data.Phase_Difference_deg_;

% 绘制幅频特性
subplot(2,1,1);
semilogx(freq, 20*log10(amplitude_ratio));
xlabel('Frequency (Hz)');
ylabel('Magnitude (dB)');
title('Amplitude Response');
grid on;

% 绘制相频特性  
subplot(2,1,2);
semilogx(freq, phase_diff);
xlabel('Frequency (Hz)');
ylabel('Phase (degrees)');
title('Phase Response');
grid on;
```

## 注意事项

1. **信号幅度**: 确保输入信号幅度在ADC量程范围内（0-3.3V）
2. **信号质量**: 使用屏蔽线缆减少干扰
3. **接地**: 确保系统良好接地
4. **扫频时间**: 完整扫频约需要1小时，请耐心等待
5. **数据存储**: 及时保存串口输出的数据

## 故障排除

### 常见问题
1. **无数据输出**: 检查串口连接和波特率设置
2. **数据异常**: 检查ADC输入信号连接
3. **扫频不启动**: 确认按键操作正确
4. **幅度过小**: 检查信号源幅度和连接

### 调试信息
系统会输出调试信息帮助定位问题：
- 采样完成状态
- FFT计算结果
- 频率设置确认
- 错误提示信息

## 系统扩展

### 可配置参数
- 扫频范围和步进
- 采样点数
- 窗函数类型
- 输出格式

### 功能扩展
- 多通道分析
- 实时频谱显示
- 自动增益控制
- 网络数据传输
