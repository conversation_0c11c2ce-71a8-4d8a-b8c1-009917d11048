# 扫频信号幅度分析配置指南

## 快速配置

在 `USER/main.c` 文件的第26-30行，你可以找到以下配置选项：

```c
// 幅度分析方法配置
#define USE_RMS_METHOD 1        // 1: 使用RMS方法, 0: 使用FFT方法
#define HIGHPASS_ALPHA 0.95f    // 高通滤波器参数 (0.9-0.99)
#define OUTPUT_PHASE_INFO 0     // 1: 输出相位信息, 0: 只输出幅度比值
```

## 配置选项详解

### 1. USE_RMS_METHOD
- **推荐值：1**
- **说明：**
  - `1`：使用RMS方法（推荐）- 快速、准确、适合扫频分析
  - `0`：使用FFT方法 - 慢但提供相位信息

### 2. HIGHPASS_ALPHA
- **推荐值：0.95**
- **范围：0.9 - 0.99**
- **说明：**
  - 数字高通滤波器的参数
  - 值越大，截止频率越低，直流抑制越强
  - 值越小，截止频率越高，可能影响低频信号

**参数对照表：**
| Alpha值 | 近似截止频率 | 适用场景 |
|---------|-------------|----------|
| 0.90    | ~40kHz      | 高频信号分析 |
| 0.95    | ~8kHz       | 通用扫频分析（推荐） |
| 0.98    | ~3kHz       | 低频信号分析 |
| 0.99    | ~1.5kHz     | 极低频信号 |

### 3. OUTPUT_PHASE_INFO
- **推荐值：0**
- **说明：**
  - `0`：只输出频率和幅度比值（推荐）
  - `1`：输出频率、幅度比值和相位差（仅FFT方法支持）

## 常用配置组合

### 配置1：快速幅频特性分析（推荐）
```c
#define USE_RMS_METHOD 1
#define HIGHPASS_ALPHA 0.95f
#define OUTPUT_PHASE_INFO 0
```
- **优点：**速度快、精度高、适合实时扫频
- **输出：**`频率(Hz),幅度比值`
- **适用：**滤波器幅频特性测试

### 配置2：完整频域分析
```c
#define USE_RMS_METHOD 0
#define HIGHPASS_ALPHA 0.95f
#define OUTPUT_PHASE_INFO 1
```
- **优点：**提供相位信息、频域分析能力
- **缺点：**速度较慢
- **输出：**`频率(Hz),幅度比值,相位差(弧度)`
- **适用：**需要相位信息的完整分析

### 配置3：低频信号分析
```c
#define USE_RMS_METHOD 1
#define HIGHPASS_ALPHA 0.98f
#define OUTPUT_PHASE_INFO 0
```
- **优点：**更强的直流抑制，适合低频信号
- **适用：**100Hz-1kHz频段的信号分析

### 配置4：高频信号分析
```c
#define USE_RMS_METHOD 1
#define HIGHPASS_ALPHA 0.90f
#define OUTPUT_PHASE_INFO 0
```
- **优点：**较高的截止频率，保留更多低频成分
- **适用：**10kHz以上的高频信号分析

## 修改配置的步骤

1. **打开文件：**`USER/main.c`
2. **找到配置区域：**第26-30行
3. **修改配置值：**根据需要修改宏定义
4. **重新编译：**编译并烧录到STM32
5. **测试验证：**运行扫频测试验证效果

## 性能对比

| 配置 | 处理时间 | 内存占用 | 精度 | 相位信息 |
|------|----------|----------|------|----------|
| RMS方法 | ~1ms | 16KB | 高 | 无 |
| FFT方法 | ~10ms | 32KB | 高 | 有 |

## 故障排除

### 问题1：幅度比值异常
- **可能原因：**直流分量过大
- **解决方案：**增大HIGHPASS_ALPHA值（如0.98）

### 问题2：低频信号衰减严重
- **可能原因：**高通滤波器截止频率过高
- **解决方案：**减小HIGHPASS_ALPHA值（如0.90）

### 问题3：处理速度太慢
- **可能原因：**使用了FFT方法
- **解决方案：**设置USE_RMS_METHOD为1

### 问题4：需要相位信息
- **解决方案：**
  ```c
  #define USE_RMS_METHOD 0
  #define OUTPUT_PHASE_INFO 1
  ```

## 验证方法

1. **使用测试脚本：**
   ```bash
   python test_rms_method.py
   ```

2. **观察串口输出：**
   - RMS方法：`频率,幅度比值`
   - FFT方法（含相位）：`频率,幅度比值,相位差`

3. **检查LCD显示：**
   - 显示当前使用的方法
   - 显示滤波器参数

## 注意事项

1. **修改配置后必须重新编译**
2. **HIGHPASS_ALPHA值不要超出0.9-0.99范围**
3. **相位信息仅在FFT方法下可用**
4. **RMS方法适合大多数应用场景**
