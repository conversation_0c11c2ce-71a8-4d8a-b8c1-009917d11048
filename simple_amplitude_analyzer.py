#!/usr/bin/env python3
"""
简化的幅频特性分析脚本
专门用于接收STM32发送的频率和幅度比值数据
"""

import serial
import csv
import time
import matplotlib.pyplot as plt
import numpy as np
from datetime import datetime

def read_amplitude_data(port='COM3', baudrate=115200, timeout=3600):
    """
    读取串口数据并解析频率和幅度比值
    
    Args:
        port: 串口号
        baudrate: 波特率
        timeout: 超时时间（秒）
    
    Returns:
        tuple: (frequencies, amplitude_ratios) 或 (None, None) 如果失败
    """
    frequencies = []
    amplitude_ratios = []
    
    try:
        # 连接串口
        ser = serial.Serial(port, baudrate, timeout=1)
        print(f"已连接到 {port}，波特率 {baudrate}")
        
        print("等待数据...")
        start_time = time.time()
        data_started = False
        
        while time.time() - start_time < timeout:
            try:
                line = ser.readline().decode('utf-8').strip()
                
                if line:
                    print(f"接收: {line}")
                    
                    # 检查是否是CSV表头
                    if "Frequency(Hz)" in line and "Amplitude_Ratio" in line:
                        data_started = True
                        print("检测到数据表头，开始记录数据")
                        continue
                    
                    # 检查是否是扫频完成标志
                    if "SWEEP_COMPLETED" in line:
                        print("扫频完成")
                        break
                    
                    # 解析数据行
                    if data_started and ',' in line:
                        try:
                            parts = line.split(',')
                            if len(parts) >= 2:
                                freq = float(parts[0])
                                ratio = float(parts[1])
                                
                                frequencies.append(freq)
                                amplitude_ratios.append(ratio)
                                
                                print(f"数据点 {len(frequencies)}: {freq:.0f}Hz, 比值={ratio:.6f}")
                        
                        except ValueError as e:
                            print(f"数据解析错误: {line} - {e}")
            
            except Exception as e:
                print(f"读取数据错误: {e}")
                break
        
        ser.close()
        print(f"数据接收完成，共收到 {len(frequencies)} 个数据点")
        
        if len(frequencies) > 0:
            return frequencies, amplitude_ratios
        else:
            return None, None
    
    except Exception as e:
        print(f"串口连接失败: {e}")
        return None, None

def save_data(frequencies, amplitude_ratios, filename=None):
    """保存数据到CSV文件"""
    if filename is None:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"amplitude_response_{timestamp}.csv"
    
    try:
        with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.writer(csvfile)
            writer.writerow(['Frequency(Hz)', 'Amplitude_Ratio'])
            
            for freq, ratio in zip(frequencies, amplitude_ratios):
                writer.writerow([freq, ratio])
        
        print(f"数据已保存到 {filename}")
        return filename
    
    except Exception as e:
        print(f"保存数据失败: {e}")
        return None

def plot_amplitude_response(frequencies, amplitude_ratios):
    """绘制幅频特性"""
    if not frequencies or not amplitude_ratios:
        print("没有数据可绘制")
        return
    
    # 转换为numpy数组
    freqs = np.array(frequencies)
    ratios = np.array(amplitude_ratios)
    
    # 创建图形
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10))
    
    # 子图1: dB刻度
    ax1.semilogx(freqs, 20*np.log10(ratios), 'b-', linewidth=2)
    ax1.set_xlabel('频率 (Hz)')
    ax1.set_ylabel('幅度 (dB)')
    ax1.set_title('滤波器幅频特性 (dB)')
    ax1.grid(True, which="both", ls="-", alpha=0.3)
    
    # 标记-3dB线
    max_gain = np.max(20*np.log10(ratios))
    cutoff_level = max_gain - 3
    ax1.axhline(y=cutoff_level, color='r', linestyle='--', alpha=0.7, label='-3dB线')
    ax1.legend()
    
    # 子图2: 线性刻度
    ax2.semilogx(freqs, ratios, 'r-', linewidth=2)
    ax2.set_xlabel('频率 (Hz)')
    ax2.set_ylabel('幅度比')
    ax2.set_title('滤波器幅频特性 (线性)')
    ax2.grid(True, which="both", ls="-", alpha=0.3)
    
    # 标记0.707线（-3dB对应的线性值）
    ax2.axhline(y=0.707, color='r', linestyle='--', alpha=0.7, label='0.707线 (-3dB)')
    ax2.legend()
    
    plt.tight_layout()
    plt.show()

def analyze_filter_characteristics(frequencies, amplitude_ratios):
    """分析滤波器特性"""
    if not frequencies or not amplitude_ratios:
        print("没有数据可分析")
        return
    
    freqs = np.array(frequencies)
    ratios = np.array(amplitude_ratios)
    ratios_db = 20 * np.log10(ratios)
    
    print("\n=== 滤波器特性分析 ===")
    print(f"频率范围: {freqs[0]:.0f} Hz - {freqs[-1]:.0f} Hz")
    print(f"数据点数: {len(freqs)}")
    
    # 通带增益
    max_gain = np.max(ratios_db)
    max_gain_freq = freqs[np.argmax(ratios_db)]
    print(f"最大增益: {max_gain:.2f} dB @ {max_gain_freq:.0f} Hz")
    
    # 寻找-3dB截止频率
    cutoff_level = max_gain - 3
    cutoff_indices = np.where(ratios_db >= cutoff_level)[0]
    
    if len(cutoff_indices) > 0:
        cutoff_freq = freqs[cutoff_indices[-1]]
        print(f"估计截止频率 (-3dB): {cutoff_freq:.0f} Hz")
        
        # 计算滚降率
        high_freq_indices = np.where(freqs > cutoff_freq * 2)[0]
        if len(high_freq_indices) > 5:
            high_freqs = freqs[high_freq_indices]
            high_gains = ratios_db[high_freq_indices]
            
            # 线性拟合计算滚降率
            coeffs = np.polyfit(np.log10(high_freqs), high_gains, 1)
            rolloff_rate = coeffs[0] * 20  # dB/decade
            
            print(f"滚降率: {rolloff_rate:.1f} dB/decade")
            
            # 估计滤波器阶数
            if abs(rolloff_rate + 20) < 5:
                print("估计滤波器阶数: 1阶")
            elif abs(rolloff_rate + 40) < 10:
                print("估计滤波器阶数: 2阶")
            elif abs(rolloff_rate + 60) < 15:
                print("估计滤波器阶数: 3阶")
            else:
                print(f"估计滤波器阶数: {abs(rolloff_rate)/20:.1f}阶")
    
    # 通带纹波
    passband_indices = np.where(ratios_db >= max_gain - 1)[0]  # 1dB范围内
    if len(passband_indices) > 1:
        passband_ripple = np.max(ratios_db[passband_indices]) - np.min(ratios_db[passband_indices])
        print(f"通带纹波: {passband_ripple:.2f} dB")

def main():
    """主函数"""
    print("STM32幅频特性分析工具")
    print("=" * 30)
    
    # 读取数据
    frequencies, amplitude_ratios = read_amplitude_data(port='COM3', baudrate=115200)
    
    if frequencies is None:
        print("未能获取有效数据")
        return
    
    # 保存数据
    filename = save_data(frequencies, amplitude_ratios)
    
    # 分析特性
    analyze_filter_characteristics(frequencies, amplitude_ratios)
    
    # 绘制图形
    plot_amplitude_response(frequencies, amplitude_ratios)
    
    print(f"\n分析完成！数据已保存到: {filename}")

if __name__ == "__main__":
    main()
