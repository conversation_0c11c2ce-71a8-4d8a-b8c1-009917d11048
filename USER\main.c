#include "stm32f4xx.h"
#include "stdio.h"
#include "usart.h"
#include "delay.h"
#include "led.h"
#include "G.h"
#include "timer.h"
#include "math.h"
#include "arm_math.h"
#include "kalman.h"
#include "fft.h"
#include "adc.h"
#include "dac.h"
#include "AD9833.h"
#include "lcd.h"
#include "stm32f4_key.h"
#include "touch.h"
#include <stdbool.h>
#include <string.h>
#include <stdlib.h>

// 数学常量定义
#ifndef PI
#define PI 3.14159265358979323846f
#endif

// 幅度分析方法配置
#define USE_RMS_METHOD 1        // 1: 使用RMS方法, 0: 使用FFT方法
#define HIGHPASS_ALPHA 0.95f    // 高通滤波器参数 (0.9-0.99)
#define OUTPUT_PHASE_INFO 0     // 1: 输出相位信息, 0: 只输出幅度比值

// Function prototypes match the declarations in adc.h
void QCZ_FFT(volatile uint16_t* buff);
void QCZ_FFT1(volatile uint16_t* buff);

// 扫频功能函数声明
void Sweep_Init(void);
void Sweep_Start(void);
void Sweep_Stop(void);
void Sweep_Update(void);
void TIM5_Sweep_Init(uint16_t arr, uint16_t psc);

// ADC采样控制函数声明
void ADC1_StartSampling(void);
void ADC1_StopSampling(void);
void ADC1_ResetSampling(void);
void ADC2_StartSampling(void);
void ADC2_StopSampling(void);
uint8_t ADC2_IsSamplingComplete(void);
// ADC3函数已移除，现在使用ADC2
// void ADC3_StartSampling(void);
// void ADC3_StopSampling(void);
// void ADC3_ResetSampling(void);

// 幅频特性分析函数声明
void AmplitudeAnalysis_Init(void);
void AmplitudeAnalysis_Start(void);
void AmplitudeAnalysis_Stop(void);
float Calculate_Signal_Amplitude_FFT(uint16_t* buffer, uint16_t size, float target_freq);
void Process_Amplitude_Data(void);
void Process_Amplitude_Data_FFT(void);  // FFT方法的备用函数
uint32_t Get_FFT_Bin_Index(float target_freq, float sample_rate, uint16_t fft_size);

// 新的RMS幅度分析函数声明
void Apply_HighPass_Filter(uint16_t* buffer, float* filtered_buffer, uint16_t size);
float Calculate_RMS_Amplitude(float* buffer, uint16_t size);
void Calculate_Signal_Amplitude_RMS(uint16_t* buffer, uint16_t size, float* amplitude);
void Calculate_Signal_Amplitude_Phase_FFT(uint16_t* buffer, uint16_t size, float target_freq, float* amplitude, float* phase);
float Convert_RMS_Amplitude_to_Voltage(float rms_amplitude);
float Convert_FFT_Amplitude_to_Voltage(float fft_amplitude);

// ADC诊断函数声明
void ADC_Diagnostics(void);
void Print_ADC_Status(void);
void ADC_Simple_Test(void);
void ADC_Manual_Test(void);
void ADC_Basic_Test(void);

// Global variables from your project
bool Separate = false;
extern int t;
extern float fft_outputbuf[FFT_LENGTH];
extern u8 Res;
uint32_t frequency_A, frequency_B;
int phase_difference_A;
int phase_difference_B;
int phase_difference_A1;
int phase_difference_B1;
extern float phase_A, phase_B, phase;
extern float frequency;
double current_output_freq_A, current_output_freq_B;
float phase_A_CS = 0.0f;
float phase_B_CS = 0.0f;
float phase_A_SX = 0.0f;
float phase_B_SX = 0.0f;
uint16_t current_phase_B = 0;
uint32_t peak_idx;
extern uint32_t peak1_idx, peak2_idx;

// Extern declarations now match the original types in your header files
extern volatile uint16_t buff_adc[];
extern volatile uint16_t buff_adc2[];
// extern volatile uint16_t buff_adc3[];  // 不再使用ADC3

// CORRECTED: Declarations are now on separate lines to match adc.h
extern volatile u8 flag_ADC;
extern volatile u8 flag_ADC1;
extern volatile u8 flag_ADC2;

extern float sampfre;
extern arm_cfft_radix4_instance_f32 scfft;

u8 QCZ = 100;
u8 QCZ1 = 0;
int QCZ_Phase[2];
int QCZ_Phase1[2];
int Phase = 0;
float ZE;
int SBP = 0;

uint16_t waveform_A, waveform_B;
uint16_t waveform_A_prime, waveform_B_prime;

char lcd_buffer[50];

// 频率显示格式化函数
void format_frequency_display(float freq, char* buffer) {
    if (freq >= 1000000.0f) {
        // 显示为MHz
        sprintf(buffer, "%.2f MHz", freq / 1000000.0f);
    } else if (freq >= 1000.0f) {
        // 显示为kHz
        sprintf(buffer, "%.1f kHz", freq / 1000.0f);
    } else {
        // 显示为Hz
        sprintf(buffer, "%.0f Hz", freq);
    }
}

// 频率控制变量
float current_frequency = 100.0;  // 当前频率，从100Hz开始
uint8_t key0_pressed = 0;         // PE4按键按下标志
uint8_t key1_pressed = 0;         // PE3按键按下标志
uint8_t frequency_changed = 1;    // 频率改变标志，用于更新显示
uint8_t dac_multiplier_changed = 1; // DAC倍数改变标志
uint8_t dac_enable_changed = 1;   // DAC使能改变标志
uint8_t adc_enable_changed = 1;   // ADC使能改变标志
uint8_t adc_user_enabled = 0;     // ADC用户使能标志（按钮控制）
// DAC使能状态通过DAC模块的dac_user_enabled变量控制
uint8_t selected_button = 0;      // 当前选中的按钮索引

// 扫频控制变量
uint8_t sweep_enabled = 0;            // 扫频使能标志
float sweep_current_freq = 1000.0f;   // 当前扫频频率
float sweep_start_freq = 1000.0f;     // 扫频起始频率 (1kHz)
float sweep_end_freq = 400200.0f;     // 扫频结束频率 (400.2kHz)
float sweep_step_freq = 200.0f;       // 扫频步进 (200Hz)
uint16_t sweep_step_count = 0;        // 当前扫频步数
uint16_t sweep_total_steps = 0;       // 总扫频步数
uint8_t sweep_direction = 1;          // 扫频方向：1=向上，0=向下
uint8_t sweep_completed = 0;          // 扫频完成标志

// ADC采样数据存储
#define ADC_SAMPLE_SIZE 4096  // 恢复4096点采样
uint16_t adc1_sample_buffer[ADC_SAMPLE_SIZE];  // ADC1采样数据缓冲区
volatile uint16_t adc1_sample_index = 0;       // 当前采样索引
volatile uint8_t adc1_sampling_complete = 0;   // 采样完成标志

uint16_t adc2_sample_buffer[ADC_SAMPLE_SIZE];  // ADC2采样数据缓冲区
volatile uint16_t adc2_sample_index = 0;       // 当前采样索引
volatile uint8_t adc2_sampling_complete = 0;   // 采样完成标志

// ADC3采样数据存储 - 已被ADC2替代，注释掉以节省内存
// #define ADC3_SAMPLE_SIZE 4096
// uint16_t adc3_sample_buffer[ADC3_SAMPLE_SIZE];  // ADC3采样数据缓冲区
// volatile uint16_t adc3_sample_index = 0;       // 当前采样索引
// volatile uint8_t adc3_sampling_complete = 0;   // 采样完成标志

// 幅频特性分析相关变量
uint8_t amplitude_analysis_enabled = 0;        // 幅频特性分析使能标志
float adc1_amplitude = 0.0f;                   // ADC1信号幅度
float adc2_amplitude = 0.0f;                   // ADC2信号幅度 (替代ADC3)
float amplitude_ratio = 0.0f;                  // 幅度比值 (ADC1/ADC2)
float amplitude_ratio_db = 0.0f;               // 幅度比值 (dB)

// 相频特性分析相关变量
float adc1_phase = 0.0f;                       // ADC1信号相位
float adc2_phase = 0.0f;                       // ADC2信号相位
float phase_difference = 0.0f;                 // 相位差 (ADC1 - ADC2)

// FFT分析相关变量 - 重用全局FFT缓冲区以节省内存
// 使用外部声明的fft_inputbuf和fft_outputbuf，分时复用

char lcd_buffer[50];              // LCD显示缓冲区

// ADC1采样控制函数声明
void ADC1_StartSampling(void);
void ADC1_StopSampling(void);
void ADC1_ResetSampling(void);

// 虚拟按钮定义
typedef struct {
    uint16_t x;      // 按钮左上角X坐标
    uint16_t y;      // 按钮左上角Y坐标
    uint16_t width;  // 按钮宽度
    uint16_t height; // 按钮高度
    char* text;      // 按钮文字
    float freq_step; // 频率步进值
    uint16_t color;  // 按钮颜色
} Button_t;

// 定义七个按钮 - 更大尺寸便于操作
Button_t buttons[7] = {
    // 第一行：频率调整按钮
    {5,   130, 90, 60, "+100kHz", 100000.0f, BLUE},
    {100, 130, 90, 60, "+10kHz",  10000.0f,  GREEN},
    {195, 130, 90, 60, "+1kHz",   1000.0f,   ORANGE},
    {290, 130, 90, 60, "+100Hz",  100.0f,    RED},
    // 第二行：DAC和ADC控制按钮
    {5,   200, 90, 60, "DAC OFF",  0.0f,     GRAY},     // DAC开关按钮
    {100, 200, 90, 60, "DAC x1.0", 0.0f,     MAGENTA},  // DAC倍数按钮
    {195, 200, 90, 60, "ADC OFF",  0.0f,     GRAY}      // ADC开关按钮
};

// 绘制按钮函数 - 支持选中和按下状态
void draw_button(Button_t* btn, uint8_t pressed, uint8_t selected) {
    uint16_t bg_color, text_color, border_color;

    if (pressed) {
        // 按下状态：红色背景，白色文字
        bg_color = RED;
        text_color = WHITE;
        border_color = RED;
    } else if (selected) {
        // 选中状态：蓝色边框，黑色文字
        bg_color = WHITE;
        text_color = BLACK;
        border_color = BLUE;
    } else {
        // 正常状态：黑色边框，黑色文字
        bg_color = WHITE;
        text_color = BLACK;
        border_color = BLACK;
    }

    // 绘制按钮背景
    lcd_fill(btn->x, btn->y, btn->x + btn->width, btn->y + btn->height, bg_color);

    // 绘制按钮边框
    lcd_draw_rectangle(btn->x, btn->y, btn->x + btn->width, btn->y + btn->height, border_color);

    // 如果是选中状态，绘制双重边框
    if (selected && !pressed) {
        lcd_draw_rectangle(btn->x + 1, btn->y + 1, btn->x + btn->width - 1, btn->y + btn->height - 1, border_color);
    }

    // 计算文字居中位置
    uint16_t text_len = strlen(btn->text);
    uint16_t text_x = btn->x + (btn->width - text_len * 6) / 2;  // 16号字体宽度约6像素
    uint16_t text_y = btn->y + (btn->height - 16) / 2;          // 16号字体高度16像素

    // 保存当前画笔颜色
    uint32_t old_color = g_point_color;

    // 设置文字颜色并显示按钮文字
    g_point_color = text_color;
    lcd_show_string(text_x, text_y, btn->width, btn->height, 16, btn->text, text_color);

    // 恢复画笔颜色
    g_point_color = old_color;
}

// 绘制所有按钮
void draw_all_buttons(uint8_t selected_index) {
    for (int i = 0; i < 7; i++) {
        draw_button(&buttons[i], 0, (i == selected_index) ? 1 : 0);
    }
}

// 检测按钮点击
int check_button_press(uint16_t touch_x, uint16_t touch_y) {
    for (int i = 0; i < 7; i++) {
        if (touch_x >= buttons[i].x && touch_x <= (buttons[i].x + buttons[i].width) &&
            touch_y >= buttons[i].y && touch_y <= (buttons[i].y + buttons[i].height)) {
            return i;  // 返回按钮索引
        }
    }
    return -1;  // 没有按钮被按下
}



// 频率调整函数
void adjust_frequency(float step) {
    current_frequency += step;

    // 检查频率范围
    if (current_frequency > 1200000.0f) {
        current_frequency = 100.0f;  // 回到100Hz
    } else if (current_frequency < 100.0f) {
        current_frequency = 100.0f;  // 最小100Hz
    }

    // 设置AD9833新的频率
    AD9833_SetFrequencyQuick1(current_frequency, AD9833_OUT_SINUS1);

    // 只有在DAC开启时才设置DAC正弦波频率
    if (DAC_GetUserEnable()) {
        DAC_SetSineFrequency(current_frequency);
    }

    frequency_changed = 1;
}

int main(void)
{
    arm_cfft_radix4_init_f32(&scfft, FFT_LENGTH, 0, 1);
    NVIC_PriorityGroupConfig(NVIC_PriorityGroup_2);
    uart_init(112500);
    delay_init(168);

    // 串口测试输出
    printf("System Starting...\r\n");
    printf("Frequency Sweep and Filter Analysis System\r\n");
    printf("ADC1: Filtered Signal (PA1)\r\n");
    printf("ADC2: Original Signal (PC1)\r\n");
    printf("Ready for frequency sweep analysis...\r\n");
    delay_ms(100);

    LED_Init();
    Adc_Init();
    Adc2_Init();     // 初始化ADC2 (PC1引脚)
    DAC_PA4_Init();  // PA4配置为DAC而不是ADC2
    DAC_SineWave_Init();  // 初始化DAC正弦波功能
    DAC_SetUserEnable(0); // 初始状态DAC用户禁用

    // Adc3_Init();  // 不再使用ADC3，已被ADC2替代

    // 初始状态关闭ADC
    ADC_Cmd(ADC1, DISABLE);
    ADC_Cmd(ADC2, DISABLE);
    // ADC_Cmd(ADC3, DISABLE);  // 不再使用ADC3
    DMA1_Init();
    // DMA2_Init();  // DMA2用于ADC2，现在PA4配置为DAC，不再需要
    // DMA3_Init();  // DMA3用于ADC3，现在ADC3已被ADC2替代，不再需要
    AD9833_Init();
    AD9833_Init1();
    key_config();  // 初始化按键

    lcd_init();
   
    sampfre = 409756;

    TIM3_Int_Init(103 - 1, 1 - 1);  // 84MHz / 103 / 1 = 815534Hz，用于ADC触发
    TIM4_Int_Init(1000 - 1, 8400 - 1);
    TIM_Cmd(TIM3, ENABLE);

    // 初始化TIM6用于DAC正弦波输出 (100kHz中断频率)
    // 84MHz / (84-1) / (10-1) = 100kHz
    TIM6_DAC_Init(10 - 1, 84 - 1);

    // 初始化TIM5用于扫频控制 (50Hz中断频率，每20ms检查一次)
    // 84MHz / (1680-1) / (1000-1) = 50Hz
    TIM5_Sweep_Init(1000 - 1, 1680 - 1);

    // 初始化扫频功能
    Sweep_Init();

    // 初始化幅频特性分析功能
    AmplitudeAnalysis_Init();

    // UI Redesign for better aesthetics and clarity
    lcd_clear(WHITE);
    g_point_color = BLACK;

    // 删除未使用的变量以消除编译警告

    // 设置默认画笔颜色
    g_point_color = BLACK;

    // 显示标题和操作提示
    lcd_show_string(10, 30, lcddev.width, 30, 16, "Frequency_out:", BLACK);

    // 绘制频率控制按钮（默认选中第一个）
    draw_all_buttons(selected_button);


    // 设置AD9833通道一产生100Hz正弦波
    AD9833_SetFrequencyQuick1(current_frequency, AD9833_OUT_SINUS1);

    // 设置DAC输出相同频率的正弦波 (0-1V范围)
    DAC_SetSineFrequency(current_frequency);

    // 立即显示初始频率
    g_point_color = BLACK;
    format_frequency_display(current_frequency, lcd_buffer);
    uint16_t str_len = strlen(lcd_buffer);
    uint16_t x_pos = (lcddev.width - str_len * 8) / 2;
    lcd_show_string(x_pos, 80, lcddev.width, 30, 16, lcd_buffer, BLACK);



    // 显示初始选中的按钮
    sprintf(lcd_buffer, "Selected: %s (%.0f)", buttons[selected_button].text, buttons[selected_button].freq_step);
    lcd_show_string(10, 50, lcddev.width, 20, 12, lcd_buffer, BLUE);

    // 标记频率已显示
    frequency_changed = 0;

    while (1)
    {
        // 检测PE4按键（KEY0）- 移动选择按钮
        if (KEY0 == 0)  // 按键按下（低电平有效）
        {
            if (key0_pressed == 0)  // 防止重复触发
            {
                key0_pressed = 1;

                // 移动到下一个按钮
                selected_button = (selected_button + 1) % 7;

                // 重新绘制所有按钮以更新选中状态
                draw_all_buttons(selected_button);

                // 显示当前选中的按钮信息
                sprintf(lcd_buffer, "Selected: %s (%.0f)", buttons[selected_button].text, buttons[selected_button].freq_step);
                lcd_show_string(10, 50, lcddev.width, 20, 12, lcd_buffer, BLUE);

                // 延时防抖
                delay_ms(200);
            }
        }
        else
        {
            key0_pressed = 0;  // 按键释放
        }

        // 检测PE3按键（KEY1）- 按下当前选中的按钮
        if (KEY1 == 0)  // 按键按下（低电平有效）
        {
            if (key1_pressed == 0)  // 防止重复触发
            {
                key1_pressed = 1;

                // 显示按钮按下效果
                draw_button(&buttons[selected_button], 1, 1);
                delay_ms(100);  // 显示按下效果

                // 执行按钮功能
                if (selected_button == 4) {
                    // DAC开关按钮
                    uint8_t current_dac_state = DAC_GetUserEnable();
                    DAC_SetUserEnable(!current_dac_state);
                    dac_enable_changed = 1;

                    // 更新按钮文本和颜色
                    if (DAC_GetUserEnable()) {
                        sprintf(buttons[4].text, "DAC ON");
                        buttons[4].color = GREEN;
                    } else {
                        sprintf(buttons[4].text, "DAC OFF");
                        buttons[4].color = GRAY;
                    }

                    // 调试信息
                    char debug_buffer[100];
                    sprintf(debug_buffer, "DAC: %s", DAC_GetUserEnable() ? "ON" : "OFF");
                    lcd_show_string(10, 70, lcddev.width, 20, 12, debug_buffer, RED);

                } else if (selected_button == 5) {
                    // DAC倍数按钮 - 只有在DAC开启时才能调整
                    if (DAC_GetUserEnable()) {
                        DAC_NextAmplitudeMultiplier();
                        dac_multiplier_changed = 1;

                        // 更新按钮文本
                        float multiplier = DAC_GetAmplitudeMultiplier();
                        sprintf(buttons[5].text, "DAC x%.1f", multiplier);

                        // 调试信息
                        char debug_buffer[100];
                        sprintf(debug_buffer, "DAC Multiplier: %.1f", multiplier);
                        lcd_show_string(10, 70, lcddev.width, 20, 12, debug_buffer, RED);
                    } else {
                        // DAC未开启时的提示
                        char debug_buffer[100];
                        sprintf(debug_buffer, "Please enable DAC first!");
                        lcd_show_string(10, 70, lcddev.width, 20, 12, debug_buffer, RED);
                    }
                } else if (selected_button == 6) {
                    // ADC开关按钮
                    adc_user_enabled = !adc_user_enabled;
                    adc_enable_changed = 1;

                    // 更新按钮文本和颜色
                    if (adc_user_enabled) {
                        sprintf(buttons[6].text, "ADC ON");
                        buttons[6].color = GREEN;

                        // 关闭DAC
                        if (DAC_GetUserEnable()) {
                            DAC_SetUserEnable(0);
                            dac_enable_changed = 1;
                            sprintf(buttons[4].text, "DAC OFF");
                            buttons[4].color = GRAY;
                        }

                        // 先运行简单的ADC测试
                        printf("Running ADC diagnostics...\r\n");
                        ADC_Diagnostics();
                        ADC_Basic_Test();   // 最基础的测试
                        ADC_Manual_Test();  // 手动触发测试
                        ADC_Simple_Test();  // 自动触发测试

                        // 重置扫频状态，允许重新开始
                        sweep_completed = 0;

                        // 启动扫频功能
                        Sweep_Start();

                        // 启动幅频特性分析
                        AmplitudeAnalysis_Start();

                        // 输出扫频参数信息
                        printf("Sweep Parameters:\r\n");
                        printf("Start Frequency: %.0f Hz\r\n", sweep_start_freq);
                        printf("End Frequency: %.0f Hz\r\n", sweep_end_freq);
                        printf("Step Frequency: %.0f Hz\r\n", sweep_step_freq);
                        printf("Total Steps: %d\r\n", sweep_total_steps);
                        printf("Sample Rate: 815534 Hz\r\n");
                        printf("FFT Size: 4096 points\r\n");

                        // ADC采样将在频率稳定后自动启动

                    } else {
                        sprintf(buttons[6].text, "ADC OFF");
                        buttons[6].color = GRAY;

                        // 停止扫频功能
                        Sweep_Stop();

                        // 停止幅频特性分析
                        AmplitudeAnalysis_Stop();

                        // 停止ADC1和ADC2
                        ADC1_StopSampling();
                        ADC2_StopSampling();
                    }

                    // 调试信息
                    char debug_buffer[100];
                    sprintf(debug_buffer, "ADC: %s", adc_user_enabled ? "ON" : "OFF");
                    lcd_show_string(10, 70, lcddev.width, 20, 12, debug_buffer, RED);

                } else {
                    // 频率调整按钮 (0-3)
                    float step_value = buttons[selected_button].freq_step;

                    // 调试信息：显示按钮详细信息
                    char debug_buffer[100];
                    sprintf(debug_buffer, "Btn:%d Step:%.0f", selected_button, step_value);
                    lcd_show_string(10, 70, lcddev.width, 20, 12, debug_buffer, RED);

                    // 只有在扫频未启用时才允许手动调整频率
                    if (!sweep_enabled) {
                        adjust_frequency(step_value);
                    } else {
                        // 扫频进行中，不允许手动调整
                        sprintf(debug_buffer, "Sweep in progress, manual adjust disabled");
                        lcd_show_string(10, 70, lcddev.width, 20, 12, debug_buffer, RED);
                    }
                }

                // 恢复按钮正常显示
                draw_all_buttons(selected_button);

                // 延时防抖
                delay_ms(200);
            }
        }
        else
        {
            key1_pressed = 0;  // 按键释放
        }

        // 触屏功能已禁用，使用物理按键控制

        // 更新LCD显示（仅在频率改变时）
        if (frequency_changed) {
            // 清除频率显示区域（不影响按钮）
            lcd_fill(0, 60, lcddev.width, 120, WHITE);

            // 重新绘制按钮（先绘制按钮）
            draw_all_buttons(selected_button);

            // 格式化频率字符串
            format_frequency_display(current_frequency, lcd_buffer);

            // 计算居中位置
            uint16_t str_len = strlen(lcd_buffer);
            uint16_t x_pos = (lcddev.width - str_len * 8) / 2;
            uint16_t y_pos = 80;  // 在按钮上方显示

            // 保存当前画笔颜色
            uint32_t old_color = g_point_color;

            // 设置文字颜色并显示频率
            g_point_color = BLACK;
            lcd_show_string(x_pos, y_pos, lcddev.width, 30, 16, lcd_buffer, BLACK);

            // 恢复画笔颜色
            g_point_color = old_color;

            frequency_changed = 0;  // 清除改变标志

            // 显示DAC状态
            if (!DAC_GetUserEnable())
            {
                sprintf(lcd_buffer, "DAC: DISABLED");
                lcd_show_string(10, 270, lcddev.width, 20, 12, lcd_buffer, GRAY);
            }
            else if (current_frequency <= 3000.0f)
            {
                float multiplier = DAC_GetAmplitudeMultiplier();
                sprintf(lcd_buffer, "DAC: ON (%.1fV out)", multiplier);
                lcd_show_string(10, 270, lcddev.width, 20, 12, lcd_buffer, GREEN);
            }
            else
            {
                sprintf(lcd_buffer, "DAC: OFF (>3kHz)");
                lcd_show_string(10, 270, lcddev.width, 20, 12, lcd_buffer, RED);
            }
        }

        // 检查DAC使能状态是否改变
        if (dac_enable_changed)
        {
            dac_enable_changed = 0;  // 清除改变标志

            // 重新绘制所有按钮以更新DAC开关按钮
            draw_all_buttons(selected_button);
        }

        // 检查DAC倍数是否改变
        if (dac_multiplier_changed)
        {
            dac_multiplier_changed = 0;  // 清除改变标志

            // 重新绘制DAC倍数按钮以更新文本
            draw_all_buttons(selected_button);
        }

        // 检查ADC使能是否改变
        if (adc_enable_changed)
        {
            adc_enable_changed = 0;  // 清除改变标志

            // 重新绘制所有按钮以更新ADC开关按钮
            draw_all_buttons(selected_button);
        }

        // 检查ADC1和ADC2采样是否都完成，进行幅频特性分析
        if (adc1_sampling_complete && adc2_sampling_complete && adc_user_enabled && amplitude_analysis_enabled)
        {
            // 处理幅频特性数据
            Process_Amplitude_Data();

            // 移除额外延时，加快扫频速度

            // 重置采样状态
            adc1_sampling_complete = 0;
            adc1_sample_index = 0;
            adc2_sampling_complete = 0;
            adc2_sample_index = 0;

            // 立即处理下一个频率点（不等待TIM5）
            if (sweep_enabled && !sweep_completed) {
                // 检查是否到达上限
                if (sweep_current_freq >= sweep_end_freq) {
                    sweep_completed = 1;
                    Sweep_Stop();
                } else {
                    // 向上扫频到下一个频率
                    sweep_current_freq += sweep_step_freq;
                    sweep_step_count++;

                    // 设置AD9833通道1新的频率
                    AD9833_SetFrequencyQuick1(sweep_current_freq, AD9833_OUT_SINUS1);

                    // 等待0.5ms让信号稳定（优化后）
                    delay_us(500);

                    // 开始新频率的ADC采样
                    ADC1_StartSampling();
                    ADC2_StartSampling();  // 使用ADC2替代ADC3
                }
            }
        }

        // 定期打印ADC状态（仅在扫频时）
        static uint32_t status_counter = 0;
        if (sweep_enabled && amplitude_analysis_enabled) {
            status_counter++;
            if (status_counter % 1000 == 0) {  // 每1000次循环打印一次
                Print_ADC_Status();
            }
        }

        delay_ms(1);  // 主循环延时（优化后）
    }
}

// ADC1采样控制函数实现
void ADC1_StartSampling(void)
{
    // 重置采样状态
    adc1_sample_index = 0;
    adc1_sampling_complete = 0;

    // 清空缓冲区（可选）
    for (int i = 0; i < ADC_SAMPLE_SIZE; i++) {
        adc1_sample_buffer[i] = 0;
    }

    // 启动ADC1
    ADC_Cmd(ADC1, ENABLE);

    // 如果使用定时器触发，确保定时器运行
    TIM_Cmd(TIM3, ENABLE);
}

void ADC1_StopSampling(void)
{
    // 停止ADC1
    ADC_Cmd(ADC1, DISABLE);

    // 可以选择停止定时器（如果不影响其他功能）
    // TIM_Cmd(TIM3, DISABLE);
}

void ADC1_ResetSampling(void)
{
    // 重置采样状态
    adc1_sample_index = 0;
    adc1_sampling_complete = 0;

    // 清空缓冲区
    for (int i = 0; i < ADC_SAMPLE_SIZE; i++) {
        adc1_sample_buffer[i] = 0;
    }
}

// ========== ADC2采样控制函数实现 ==========

void ADC2_StartSampling(void)
{
    // 重置采样状态
    adc2_sample_index = 0;
    adc2_sampling_complete = 0;

    // 清空缓冲区（可选）
    for (int i = 0; i < ADC_SAMPLE_SIZE; i++) {
        adc2_sample_buffer[i] = 0;
    }

    // 启动ADC2
    ADC_Cmd(ADC2, ENABLE);

    // 如果使用定时器触发，确保定时器运行
    TIM_Cmd(TIM3, ENABLE);
}

uint8_t ADC2_IsSamplingComplete(void)
{
    return adc2_sampling_complete;
}

void ADC2_StopSampling(void)
{
    // 停止ADC2
    ADC_Cmd(ADC2, DISABLE);

    // 重置采样状态
    adc2_sample_index = 0;
    adc2_sampling_complete = 0;

    // 清空缓冲区
    for (int i = 0; i < ADC_SAMPLE_SIZE; i++) {
        adc2_sample_buffer[i] = 0;
    }
}

// ========== ADC3采样控制函数实现 - 已被ADC2替代，注释掉 ==========

/*
// ADC3功能已被ADC2替代，以下函数不再使用
void ADC3_StartSampling(void)
{
    // 重置采样状态
    adc3_sample_index = 0;
    adc3_sampling_complete = 0;

    // 清空缓冲区（可选）
    for (int i = 0; i < ADC3_SAMPLE_SIZE; i++) {
        adc3_sample_buffer[i] = 0;
    }

    // 启动ADC3
    ADC_Cmd(ADC3, ENABLE);

    // TIM3已经在ADC1启动时启动了
}

void ADC3_StopSampling(void)
{
    // 停止ADC3
    ADC_Cmd(ADC3, DISABLE);
}

void ADC3_ResetSampling(void)
{
    // 重置采样状态
    adc3_sample_index = 0;
    adc3_sampling_complete = 0;

    // 清空缓冲区
    for (int i = 0; i < ADC3_SAMPLE_SIZE; i++) {
        adc3_sample_buffer[i] = 0;
    }
}
*/

// ========== 扫频功能实现 ==========

/**
 * @brief  初始化扫频功能
 * @param  None
 * @retval None
 */
void Sweep_Init(void)
{
    sweep_enabled = 0;
    sweep_current_freq = sweep_start_freq;
    sweep_step_count = 0;
    sweep_direction = 1;  // 向上扫频
    sweep_completed = 0;  // 扫频未完成

    // 计算总步数
    sweep_total_steps = (uint16_t)((sweep_end_freq - sweep_start_freq) / sweep_step_freq) + 1;
}

/**
 * @brief  启动扫频功能
 * @param  None
 * @retval None
 */
void Sweep_Start(void)
{
    if (!sweep_enabled && !sweep_completed) {
        sweep_enabled = 1;
        sweep_current_freq = sweep_start_freq;
        sweep_step_count = 0;
        sweep_direction = 1;
        sweep_completed = 0;

        printf("Sweep Start: %.0f Hz to %.0f Hz, Step: %.0f Hz\r\n",
               sweep_start_freq, sweep_end_freq, sweep_step_freq);

        // 设置AD9833通道1输出起始频率
        AD9833_SetFrequencyQuick1(sweep_current_freq, AD9833_OUT_SINUS1);

        // 等待2ms让信号稳定
        delay_ms(2);

        // 打印ADC诊断信息
        ADC_Diagnostics();

        // 开始第一个频率点的ADC采样
        extern void ADC1_StartSampling(void);
        extern void ADC2_StartSampling(void);
        ADC1_StartSampling();
        ADC2_StartSampling();

        printf("ADC Sampling Started\r\n");
        Print_ADC_Status();

        // 启动TIM5定时器
        TIM_Cmd(TIM5, ENABLE);
    }
}

/**
 * @brief  停止扫频功能
 * @param  None
 * @retval None
 */
void Sweep_Stop(void)
{
    if (sweep_enabled) {
        sweep_enabled = 0;

        // 停止TIM5定时器
        TIM_Cmd(TIM5, DISABLE);

        // 停止幅频特性分析
        AmplitudeAnalysis_Stop();

        // 停止ADC采样
        extern uint8_t adc_user_enabled;
        if (adc_user_enabled) {
            extern void ADC1_StopSampling(void);
            extern void ADC2_StopSampling(void);
            ADC1_StopSampling();
            ADC2_StopSampling();
        }

        // 恢复到原来的频率
        AD9833_SetFrequencyQuick1(current_frequency, AD9833_OUT_SINUS1);

        // 输出扫频完成信息
        if (sweep_completed) {
            printf("SWEEP_COMPLETED\r\n");
            printf("Total frequency points analyzed: %d\r\n", sweep_step_count);
            printf("Frequency range: %.0f Hz to %.0f Hz\r\n", sweep_start_freq, sweep_end_freq);
            printf("Analysis complete. Data ready for processing.\r\n");
        }
    }
}

/**
 * @brief  更新扫频频率
 * @param  None
 * @retval None
 * @note   由TIM5中断调用
 */
void Sweep_Update(void)
{
    // TIM5中断现在主要用于监控，实际扫频逻辑在主循环中处理
    // 这里可以添加一些监控或超时检查
    if (!sweep_enabled || sweep_completed) {
        return;
    }

    // 可以在这里添加超时检查或其他监控逻辑
}

/**
 * @brief  初始化TIM5用于扫频控制
 * @param  arr: 自动重装载值
 * @param  psc: 预分频值
 * @retval None
 */
void TIM5_Sweep_Init(uint16_t arr, uint16_t psc)
{
    TIM_TimeBaseInitTypeDef TIM_TimeBaseInitStructure;
    NVIC_InitTypeDef NVIC_InitStructure;

    // 使能TIM5时钟
    RCC_APB1PeriphClockCmd(RCC_APB1Periph_TIM5, ENABLE);

    // 配置TIM5基本参数
    TIM_TimeBaseInitStructure.TIM_Period = arr;        // 自动重装载值
    TIM_TimeBaseInitStructure.TIM_Prescaler = psc;     // 预分频值
    TIM_TimeBaseInitStructure.TIM_CounterMode = TIM_CounterMode_Up;  // 向上计数
    TIM_TimeBaseInitStructure.TIM_ClockDivision = TIM_CKD_DIV1;

    TIM_TimeBaseInit(TIM5, &TIM_TimeBaseInitStructure);

    // 使能TIM5更新中断
    TIM_ITConfig(TIM5, TIM_IT_Update, ENABLE);

    // 配置NVIC
    NVIC_InitStructure.NVIC_IRQChannel = TIM5_IRQn;
    NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 0x03;  // 抢占优先级3
    NVIC_InitStructure.NVIC_IRQChannelSubPriority = 0x01;         // 子优先级1
    NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;
    NVIC_Init(&NVIC_InitStructure);

    // 不立即启动TIM5，等待扫频开始时启动
    TIM_Cmd(TIM5, DISABLE);
}

// ========== 幅频特性分析功能实现 ==========

/**
 * @brief  初始化幅频特性分析功能
 * @param  None
 * @retval None
 */
void AmplitudeAnalysis_Init(void)
{
    amplitude_analysis_enabled = 0;
    adc1_amplitude = 0.0f;
    adc2_amplitude = 0.0f;  // 使用ADC2替代ADC3
    amplitude_ratio = 0.0f;
    amplitude_ratio_db = 0.0f;
}

/**
 * @brief  启动幅频特性分析
 * @param  None
 * @retval None
 */
void AmplitudeAnalysis_Start(void)
{
    amplitude_analysis_enabled = 1;

    // 输出简化的表头 - 只输出频率和幅度比值
    printf("Frequency(Hz),Amplitude_Ratio\r\n");
}

/**
 * @brief  停止幅频特性分析
 * @param  None
 * @retval None
 */
void AmplitudeAnalysis_Stop(void)
{
    amplitude_analysis_enabled = 0;
}

/**
 * @brief  获取FFT频率仓索引
 * @param  target_freq: 目标频率 (Hz)
 * @param  sample_rate: 采样率 (Hz)
 * @param  fft_size: FFT大小
 * @retval FFT频率仓索引
 */
uint32_t Get_FFT_Bin_Index(float target_freq, float sample_rate, uint16_t fft_size)
{
    // 频率分辨率 = 采样率 / FFT大小
    float freq_resolution = sample_rate / (float)fft_size;

    // 计算目标频率对应的频率仓索引
    uint32_t bin_index = (uint32_t)(target_freq / freq_resolution + 0.5f);

    // 限制在有效范围内
    if (bin_index >= fft_size/2) {
        bin_index = fft_size/2 - 1;
    }



    return bin_index;
}

/**
 * @brief  数字高通滤波器去除直流分量
 * @param  buffer: 采样数据缓冲区
 * @param  filtered_buffer: 滤波后数据缓冲区
 * @param  size: 缓冲区大小
 * @retval None
 * @note   使用简单的一阶高通滤波器：y[n] = α*(y[n-1] + x[n] - x[n-1])
 */
void Apply_HighPass_Filter(uint16_t* buffer, float* filtered_buffer, uint16_t size)
{
    // 高通滤波器参数，可通过宏定义配置
    const float alpha = HIGHPASS_ALPHA;

    // 初始化
    float prev_input = (float)buffer[0];
    float prev_output = 0.0f;

    // 第一个点
    filtered_buffer[0] = 0.0f;

    // 应用高通滤波器
    for (uint16_t i = 1; i < size; i++) {
        float current_input = (float)buffer[i];

        // 高通滤波器公式：y[n] = α*(y[n-1] + x[n] - x[n-1])
        filtered_buffer[i] = alpha * (prev_output + current_input - prev_input);

        prev_input = current_input;
        prev_output = filtered_buffer[i];
    }
}

/**
 * @brief  计算信号的RMS幅度
 * @param  buffer: 信号数据缓冲区
 * @param  size: 缓冲区大小
 * @retval RMS幅度值
 */
float Calculate_RMS_Amplitude(float* buffer, uint16_t size)
{
    float sum_squares = 0.0f;

    // 计算平方和
    for (uint16_t i = 0; i < size; i++) {
        sum_squares += buffer[i] * buffer[i];
    }

    // 计算RMS值
    float rms = sqrtf(sum_squares / (float)size);

    return rms;
}

/**
 * @brief  使用高通滤波器和RMS计算信号幅度（替代FFT方法）
 * @param  buffer: 采样数据缓冲区
 * @param  size: 缓冲区大小
 * @param  amplitude: 输出幅度值
 * @retval None
 * @note   这种方法比FFT更快，更适合扫频信号分析
 */
void Calculate_Signal_Amplitude_RMS(uint16_t* buffer, uint16_t size, float* amplitude)
{
    // 使用静态缓冲区避免重复分配内存
    static float filtered_buffer[4096];

    // 检查缓冲区大小
    if (size > 4096) {
        *amplitude = 0.0f;
        return;
    }

    // 应用高通滤波器去除直流分量
    Apply_HighPass_Filter(buffer, filtered_buffer, size);

    // 计算RMS幅度
    *amplitude = Calculate_RMS_Amplitude(filtered_buffer, size);
}

/**
 * @brief  使用FFT计算特定频率的信号幅度和相位（保留作为备用方法）
 * @param  buffer: 采样数据缓冲区
 * @param  size: 缓冲区大小
 * @param  target_freq: 目标频率 (Hz)
 * @param  amplitude: 输出幅度值
 * @param  phase: 输出相位值（弧度）
 * @retval None
 */
void Calculate_Signal_Amplitude_Phase_FFT(uint16_t* buffer, uint16_t size, float target_freq,
                                          float* amplitude, float* phase)
{
    // 重用全局FFT缓冲区以节省内存
    extern float fft_inputbuf[];
    extern float fft_outputbuf[];
    extern arm_cfft_radix4_instance_f32 scfft;

    // 准备FFT输入数据（转换为复数格式）
    // 去掉直流分量以提高精度
    float dc_offset = 0.0f;
    for (uint16_t i = 0; i < size; i++) {
        dc_offset += (float)buffer[i];
    }
    dc_offset /= (float)size;

    for (uint16_t i = 0; i < size; i++) {
        fft_inputbuf[2*i] = (float)buffer[i] - dc_offset;  // 去掉直流分量
        fft_inputbuf[2*i+1] = 0.0f; // 虚部为0
    }

    // 执行FFT
    arm_cfft_radix4_f32(&scfft, fft_inputbuf);

    // 计算幅度谱
    arm_cmplx_mag_f32(fft_inputbuf, fft_outputbuf, size);

    // 获取目标频率对应的频率仓索引
    // 采样率约为815534Hz (TIM3触发频率)
    float sample_rate = 815534.0f;
    uint32_t bin_index = Get_FFT_Bin_Index(target_freq, sample_rate, size);

    // 检查频率仓索引是否合理
    if (bin_index >= size/2) {
        *amplitude = 0.0f;
        *phase = 0.0f;
        return;
    }

    // 在目标频率左右共五个点中选择最高的作为目标频率仓
    float max_amplitude = 0.0f;
    uint32_t max_bin = bin_index;

    // 检查左右五个点：bin_index-2, bin_index-1, bin_index, bin_index+1, bin_index+2
    for (int offset = -2; offset <= 2; offset++) {
        uint32_t check_bin = bin_index + offset;

        // 确保频率仓索引在有效范围内
        if (check_bin < size/2) {
            float current_amplitude = fft_outputbuf[check_bin];

            // 对于实信号，需要乘以2（除了DC和Nyquist频率）
            if (check_bin > 0 && check_bin < size/2) {
                current_amplitude *= 2.0f;
            }

            // 选择最大幅度
            if (current_amplitude > max_amplitude) {
                max_amplitude = current_amplitude;
                max_bin = check_bin;
            }
        }
    }

    // 输出幅度值
    *amplitude = max_amplitude;

    // 计算相位（使用最大幅度对应的频率仓）
    if (max_bin < size/2) {
        float real_part = fft_inputbuf[2*max_bin];
        float imag_part = fft_inputbuf[2*max_bin+1];
        *phase = atan2f(imag_part, real_part);  // 相位（弧度）
    } else {
        *phase = 0.0f;
    }
}

/**
 * @brief  将RMS幅度值转换为电压幅度
 * @param  rms_amplitude: RMS计算得到的幅度值
 * @retval 电压幅度值 (V)
 */
float Convert_RMS_Amplitude_to_Voltage(float rms_amplitude)
{
    // STM32F4 ADC配置参数
    const float ADC_VREF = 3.3f;        // ADC参考电压 (V)
    const uint32_t ADC_RESOLUTION = 4096; // 12位ADC分辨率 (2^12)

    // RMS幅度转换为电压幅度的计算：
    // 1. RMS幅度值是经过高通滤波后的RMS值
    // 2. ADC数字值转电压：voltage = (adc_value / ADC_RESOLUTION) * ADC_VREF
    // 3. 由于是RMS值，如果需要峰值可以乘以sqrt(2)

    // 计算电压幅度 (RMS值)
    float voltage_amplitude = (rms_amplitude * ADC_VREF) / ADC_RESOLUTION;

    return voltage_amplitude;
}

/**
 * @brief  将FFT幅度值转换为电压幅度（保留作为备用方法）
 * @param  fft_amplitude: FFT计算得到的幅度值
 * @retval 电压幅度值 (V)
 */
float Convert_FFT_Amplitude_to_Voltage(float fft_amplitude)
{
    // STM32F4 ADC配置参数
    const float ADC_VREF = 3.3f;        // ADC参考电压 (V)
    const uint32_t ADC_RESOLUTION = 4096; // 12位ADC分辨率 (2^12)
    const uint32_t FFT_SIZE = 4096;      // FFT点数

    // FFT幅度转换为电压幅度的计算：
    // 1. FFT幅度值对应的是信号的RMS幅度
    // 2. 需要转换为峰值幅度：peak = rms * sqrt(2)
    // 3. ADC数字值转电压：voltage = (adc_value / ADC_RESOLUTION) * ADC_VREF
    // 4. FFT归一化：考虑FFT点数的影响

    // 计算电压幅度 (峰值)
    float voltage_amplitude = (fft_amplitude * ADC_VREF) / (ADC_RESOLUTION * FFT_SIZE / 2);

    return voltage_amplitude;
}

/**
 * @brief  处理幅频特性数据（根据配置选择RMS或FFT方法）
 * @param  None
 * @retval None
 * @note   当ADC1和ADC2都采样完成时调用
 */
void Process_Amplitude_Data(void)
{
    if (!amplitude_analysis_enabled) {
        return;
    }

    // 数据有效性检查
    if (adc1_sample_index < ADC_SAMPLE_SIZE || adc2_sample_index < ADC_SAMPLE_SIZE) {
        printf("ERROR: Incomplete sampling - ADC1:%d, ADC2:%d\r\n", adc1_sample_index, adc2_sample_index);
        return;
    }

#if USE_RMS_METHOD
    // 使用RMS方法分析ADC1和ADC2的幅度（推荐方法）
    // 先分析ADC1（滤波后信号）
    Calculate_Signal_Amplitude_RMS(adc1_sample_buffer, ADC_SAMPLE_SIZE, &adc1_amplitude);

    // 再分析ADC2（原始信号）
    Calculate_Signal_Amplitude_RMS(adc2_sample_buffer, ADC_SAMPLE_SIZE, &adc2_amplitude);

    // 转换为电压幅度
    float adc1_voltage = Convert_RMS_Amplitude_to_Voltage(adc1_amplitude);
    float adc2_voltage = Convert_RMS_Amplitude_to_Voltage(adc2_amplitude);

    // RMS方法不提供相位信息
    phase_difference = 0.0f;

#else
    // 使用FFT方法分析ADC1和ADC2的幅度和相位
    // 先分析ADC1（滤波后信号）
    Calculate_Signal_Amplitude_Phase_FFT(adc1_sample_buffer, ADC_SAMPLE_SIZE, sweep_current_freq,
                                         &adc1_amplitude, &adc1_phase);

    // 再分析ADC2（原始信号）
    Calculate_Signal_Amplitude_Phase_FFT(adc2_sample_buffer, ADC_SAMPLE_SIZE, sweep_current_freq,
                                         &adc2_amplitude, &adc2_phase);

    // 转换为电压幅度
    float adc1_voltage = Convert_FFT_Amplitude_to_Voltage(adc1_amplitude);
    float adc2_voltage = Convert_FFT_Amplitude_to_Voltage(adc2_amplitude);

    // 计算相位差（滤波器的相频特性）
    phase_difference = adc1_phase - adc2_phase;  // 滤波后相位 - 原始信号相位

    // 将相位差规范化到 [-π, π] 范围
    while (phase_difference > PI) {
        phase_difference -= 2.0f * PI;
    }
    while (phase_difference < -PI) {
        phase_difference += 2.0f * PI;
    }
#endif

    // 计算幅度比（滤波器的幅频特性）
    amplitude_ratio = 0.0f;
    if (adc2_voltage > 1e-6f) {  // 避免除零
        amplitude_ratio = adc1_voltage / adc2_voltage;  // 滤波后/原始信号
    }

    // 根据配置输出数据
#if OUTPUT_PHASE_INFO && !USE_RMS_METHOD
    // 输出频率、幅度比值和相位差（仅FFT方法支持）
    printf("%.0f,%.6f,%.6f\r\n", sweep_current_freq, amplitude_ratio, phase_difference);
#else
    // 只输出频率和幅度比值
    printf("%.0f,%.6f\r\n", sweep_current_freq, amplitude_ratio);
#endif

    // 在LCD上显示当前频率信息
    char display_buffer[100];
    sprintf(display_buffer, "Freq: %.0fHz", sweep_current_freq);
    lcd_show_string(10, 90, lcddev.width, 20, 12, display_buffer, BLUE);

    // 显示幅度比值
    sprintf(display_buffer, "Amplitude Ratio: %.6f", amplitude_ratio);
    lcd_show_string(10, 110, lcddev.width, 20, 12, display_buffer, GREEN);

    // 显示当前使用的方法
#if USE_RMS_METHOD
    sprintf(display_buffer, "Method: RMS (α=%.2f)", HIGHPASS_ALPHA);
#else
    sprintf(display_buffer, "Method: FFT");
#endif
    lcd_show_string(10, 150, lcddev.width, 20, 12, display_buffer, CYAN);

    sprintf(display_buffer, "Step: %d/%d", sweep_step_count, sweep_total_steps);
    lcd_show_string(10, 130, lcddev.width, 20, 12, display_buffer, BLUE);
}

/**
 * @brief  处理幅频和相频特性数据（使用FFT方法，保留作为备用）
 * @param  None
 * @retval None
 * @note   当需要相位信息时可以调用此函数
 */
void Process_Amplitude_Data_FFT(void)
{
    if (!amplitude_analysis_enabled) {
        return;
    }

    // 数据有效性检查
    if (adc1_sample_index < ADC_SAMPLE_SIZE || adc2_sample_index < ADC_SAMPLE_SIZE) {
        printf("ERROR: Incomplete sampling - ADC1:%d, ADC2:%d\r\n", adc1_sample_index, adc2_sample_index);
        return;
    }

    // 分别对ADC1和ADC2进行FFT分析，获取幅度和相位
    // 先分析ADC1（滤波后信号）
    Calculate_Signal_Amplitude_Phase_FFT(adc1_sample_buffer, ADC_SAMPLE_SIZE, sweep_current_freq,
                                         &adc1_amplitude, &adc1_phase);

    // 再分析ADC2（原始信号）
    Calculate_Signal_Amplitude_Phase_FFT(adc2_sample_buffer, ADC_SAMPLE_SIZE, sweep_current_freq,
                                         &adc2_amplitude, &adc2_phase);

    // 转换为电压幅度
    float adc1_voltage = Convert_FFT_Amplitude_to_Voltage(adc1_amplitude);
    float adc2_voltage = Convert_FFT_Amplitude_to_Voltage(adc2_amplitude);

    // 计算幅度比（滤波器的幅频特性）
    amplitude_ratio = 0.0f;
    if (adc2_voltage > 1e-6f) {  // 避免除零
        amplitude_ratio = adc1_voltage / adc2_voltage;  // 滤波后/原始信号
    }

    // 计算相位差（滤波器的相频特性）
    phase_difference = adc1_phase - adc2_phase;  // 滤波后相位 - 原始信号相位

    // 将相位差规范化到 [-π, π] 范围
    while (phase_difference > PI) {
        phase_difference -= 2.0f * PI;
    }
    while (phase_difference < -PI) {
        phase_difference += 2.0f * PI;
    }

    // 输出频率、幅度比值和相位差
    printf("%.0f,%.6f,%.6f\r\n", sweep_current_freq, amplitude_ratio, phase_difference);

    // 在LCD上显示当前频率信息
    char display_buffer[100];
    sprintf(display_buffer, "Freq: %.0fHz", sweep_current_freq);
    lcd_show_string(10, 90, lcddev.width, 20, 12, display_buffer, BLUE);

    // 显示幅度比值
    sprintf(display_buffer, "Amplitude Ratio: %.6f", amplitude_ratio);
    lcd_show_string(10, 110, lcddev.width, 20, 12, display_buffer, GREEN);

    sprintf(display_buffer, "Step: %d/%d", sweep_step_count, sweep_total_steps);
    lcd_show_string(10, 130, lcddev.width, 20, 12, display_buffer, BLUE);
}

// ========== ADC诊断函数 ==========

/**
 * @brief  ADC诊断函数 - 检查ADC状态和配置
 * @param  None
 * @retval None
 */
void ADC_Diagnostics(void)
{
    printf("\n=== ADC Diagnostics ===\r\n");

    // Check ADC enable status
    printf("ADC1 Enabled: %s\r\n", (ADC1->CR2 & ADC_CR2_ADON) ? "Yes" : "No");
    printf("ADC2 Enabled: %s\r\n", (ADC2->CR2 & ADC_CR2_ADON) ? "Yes" : "No");

    // Check ADC interrupt configuration
    printf("ADC1 EOC IRQ: %s\r\n", (ADC1->CR1 & ADC_CR1_EOCIE) ? "Enabled" : "Disabled");
    printf("ADC2 EOC IRQ: %s\r\n", (ADC2->CR1 & ADC_CR1_EOCIE) ? "Enabled" : "Disabled");

    // Check external trigger configuration
    printf("ADC1 Ext Trigger: 0x%08X\r\n", (uint32_t)(ADC1->CR2 & (ADC_CR2_EXTSEL | ADC_CR2_EXTEN)));
    printf("ADC2 Ext Trigger: 0x%08X\r\n", (uint32_t)(ADC2->CR2 & (ADC_CR2_EXTSEL | ADC_CR2_EXTEN)));

    // Check NVIC interrupt enable
    printf("ADC IRQ NVIC: %s\r\n", (NVIC->ISER[ADC_IRQn >> 5] & (1 << (ADC_IRQn & 0x1F))) ? "Enabled" : "Disabled");

    // Check ADC status registers
    printf("ADC1 Status: 0x%08X\r\n", (uint32_t)ADC1->SR);
    printf("ADC2 Status: 0x%08X\r\n", (uint32_t)ADC2->SR);

    // Check TIM3 status
    printf("TIM3 Enabled: %s\r\n", (TIM3->CR1 & TIM_CR1_CEN) ? "Yes" : "No");
    printf("TIM3 Counter: %d\r\n", TIM3->CNT);
    printf("TIM3 ARR: %d\r\n", TIM3->ARR);
    printf("TIM3 TRGO: 0x%08X\r\n", (uint32_t)(TIM3->CR2 & TIM_CR2_MMS));

    // Check sampling status
    printf("ADC1 Sample Index: %d, Complete: %d\r\n", adc1_sample_index, adc1_sampling_complete);
    printf("ADC2 Sample Index: %d, Complete: %d\r\n", adc2_sample_index, adc2_sampling_complete);

    // Check ADC data registers
    printf("ADC1 Data Reg: %d\r\n", ADC1->DR);
    printf("ADC2 Data Reg: %d\r\n", ADC2->DR);

    // Check ADC Common register
    printf("ADC Common Mode: 0x%08X\r\n", (uint32_t)(ADC->CCR & ADC_CCR_MULTI));

    printf("=======================\r\n\n");
}

/**
 * @brief  打印ADC状态信息
 * @param  None
 * @retval None
 */
void Print_ADC_Status(void)
{
    printf("ADC Status: ADC1[%d/%d] ADC2[%d/%d] Complete[%d,%d]\r\n",
           adc1_sample_index, ADC_SAMPLE_SIZE,
           adc2_sample_index, ADC_SAMPLE_SIZE,
           adc1_sampling_complete, adc2_sampling_complete);
}

/**
 * @brief  简单的ADC测试函数 - 测试100个采样点
 * @param  None
 * @retval None
 */
void ADC_Simple_Test(void)
{
    printf("\n=== ADC Simple Test ===\r\n");

    // 重置采样状态
    adc1_sample_index = 0;
    adc1_sampling_complete = 0;
    adc2_sample_index = 0;
    adc2_sampling_complete = 0;

    // 启动ADC
    ADC_Cmd(ADC1, ENABLE);
    ADC_Cmd(ADC2, ENABLE);

    // 启动TIM3
    TIM_Cmd(TIM3, ENABLE);

    printf("ADC Test Started - Collecting 100 samples...\r\n");

    // 等待采样100个点或超时
    uint32_t timeout = 0;
    while ((adc1_sample_index < 100 || adc2_sample_index < 100) && timeout < 10000) {
        delay_ms(1);
        timeout++;

        // 每100ms打印一次状态
        if (timeout % 100 == 0) {
            printf("Progress: ADC1=%d, ADC2=%d\r\n", adc1_sample_index, adc2_sample_index);
        }
    }

    // 停止ADC
    ADC_Cmd(ADC1, DISABLE);
    ADC_Cmd(ADC2, DISABLE);

    // 打印结果
    printf("Test Complete: ADC1=%d, ADC2=%d samples in %dms\r\n",
           adc1_sample_index, adc2_sample_index, timeout);

    // 打印前10个采样值
    printf("First 10 ADC1 values: ");
    for (int i = 0; i < 10 && i < adc1_sample_index; i++) {
        printf("%d ", adc1_sample_buffer[i]);
    }
    printf("\r\n");

    printf("First 10 ADC2 values: ");
    for (int i = 0; i < 10 && i < adc2_sample_index; i++) {
        printf("%d ", adc2_sample_buffer[i]);
    }
    printf("\r\n");

    printf("=======================\r\n\n");
}

/**
 * @brief  手动ADC测试函数 - 使用软件触发
 * @param  None
 * @retval None
 */
void ADC_Manual_Test(void)
{
    printf("\n=== ADC Manual Test ===\r\n");

    // 重置采样状态
    adc1_sample_index = 0;
    adc1_sampling_complete = 0;
    adc2_sample_index = 0;
    adc2_sampling_complete = 0;

    // 启动ADC
    ADC_Cmd(ADC1, ENABLE);
    ADC_Cmd(ADC2, ENABLE);

    printf("Manual ADC Test - Software trigger mode\r\n");

    // 手动触发10次转换
    for (int i = 0; i < 10; i++) {
        printf("Trigger %d: ", i+1);

        // 软件触发ADC1
        ADC_SoftwareStartConv(ADC1);
        delay_ms(1);  // 等待转换完成
        uint16_t adc1_val = ADC_GetConversionValue(ADC1);

        // 软件触发ADC2
        ADC_SoftwareStartConv(ADC2);
        delay_ms(1);  // 等待转换完成
        uint16_t adc2_val = ADC_GetConversionValue(ADC2);

        printf("ADC1=%d, ADC2=%d\r\n", adc1_val, adc2_val);

        delay_ms(100);  // 间隔100ms
    }

    // 停止ADC
    ADC_Cmd(ADC1, DISABLE);
    ADC_Cmd(ADC2, DISABLE);

    printf("Manual test complete\r\n");
    printf("=======================\r\n\n");
}

/**
 * @brief  基础ADC测试函数 - 最简单的ADC读取测试
 * @param  None
 * @retval None
 */
void ADC_Basic_Test(void)
{
    printf("\n=== ADC Basic Test ===\r\n");

    // 简单启动ADC并读取几个值
    ADC_Cmd(ADC1, ENABLE);
    ADC_Cmd(ADC2, ENABLE);

    printf("Reading ADC values directly...\r\n");

    for (int i = 0; i < 5; i++) {
        // 直接读取ADC数据寄存器
        uint16_t adc1_val = ADC1->DR;
        uint16_t adc2_val = ADC2->DR;

        printf("Read %d: ADC1_DR=%d, ADC2_DR=%d\r\n", i+1, adc1_val, adc2_val);

        delay_ms(500);  // 间隔500ms
    }

    // 停止ADC
    ADC_Cmd(ADC1, DISABLE);
    ADC_Cmd(ADC2, DISABLE);

    printf("Basic test complete\r\n");
    printf("======================\r\n\n");
}