Component: ARM Compiler 5.06 update 7 (build 960) Tool: armlink [4d3601]

==============================================================================

Section Cross References

    main.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    main.o(.text) refers to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    main.o(.text) refers to printf1.o(x$fpl$printf1) for _printf_fp_dec
    main.o(.text) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    main.o(.text) refers to _printf_dec.o(.text) for _printf_int_dec
    main.o(.text) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    main.o(.text) refers to _printf_str.o(.text) for _printf_str
    main.o(.text) refers to _printf_pad.o(.text) for _printf_pre_padding
    main.o(.text) refers to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    main.o(.text) refers to _printf_hex_int.o(.text) for _printf_longlong_hex
    main.o(.text) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    main.o(.text) refers to noretval__2sprintf.o(.text) for __2sprintf
    main.o(.text) refers to lcd.o(.text) for lcd_fill
    main.o(.text) refers to strlen.o(.text) for strlen
    main.o(.text) refers to ad9833.o(.text) for AD9833_SetFrequencyQuick1
    main.o(.text) refers to dac.o(.text) for DAC_GetUserEnable
    main.o(.text) refers to noretval__2printf.o(.text) for __2printf
    main.o(.text) refers to stm32f4xx_adc.o(.text) for ADC_Cmd
    main.o(.text) refers to stm32f4xx_tim.o(.text) for TIM_Cmd
    main.o(.text) refers to lcd.o(.data) for g_point_color
    main.o(.text) refers to main.o(.data) for buttons
    main.o(.text) refers to main.o(.bss) for adc2_sample_buffer
    main.o(.text) refers to sqrtf.o(i.__hardfp_sqrtf) for __hardfp_sqrtf
    main.o(.text) refers to lcd.o(.bss) for lcddev
    main.o(.text) refers to delay.o(.text) for delay_ms
    main.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB1PeriphClockCmd
    main.o(.text) refers to misc.o(.text) for NVIC_Init
    main.o(.text) refers to arm_cfft_radix4_init_f32.o(.text) for arm_cfft_radix4_init_f32
    main.o(.text) refers to usart.o(.text) for uart_init
    main.o(.text) refers to led.o(.text) for LED_Init
    main.o(.text) refers to adc.o(.text) for Adc_Init
    main.o(.text) refers to stm32f4_key.o(.text) for key_config
    main.o(.text) refers to timer.o(.text) for TIM3_Int_Init
    main.o(.text) refers to fft.o(.bss) for scfft
    main.o(.text) refers to fft.o(.data) for sampfre
    main.o(.text) refers to arm_cfft_radix4_f32.o(.text) for arm_cfft_radix4_f32
    main.o(.text) refers to arm_cmplx_mag_f32.o(.text) for arm_cmplx_mag_f32
    main.o(.text) refers to atan2f.o(i.__hardfp_atan2f) for __hardfp_atan2f
    main.o(.data) refers to main.o(.conststring) for .conststring
    system_stm32f4xx.o(.text) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    startup_stm32f40_41xxx.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f40_41xxx.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f40_41xxx.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f40_41xxx.o(RESET) refers to startup_stm32f40_41xxx.o(STACK) for __initial_sp
    startup_stm32f40_41xxx.o(RESET) refers to startup_stm32f40_41xxx.o(.text) for Reset_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(.text) for NMI_Handler
    startup_stm32f40_41xxx.o(RESET) refers to adc.o(.text) for ADC_IRQHandler
    startup_stm32f40_41xxx.o(RESET) refers to timer.o(.text) for TIM4_IRQHandler
    startup_stm32f40_41xxx.o(RESET) refers to usart.o(.text) for USART1_IRQHandler
    startup_stm32f40_41xxx.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f40_41xxx.o(.text) refers to system_stm32f4xx.o(.text) for SystemInit
    startup_stm32f40_41xxx.o(.text) refers to __main.o(!!!main) for __main
    startup_stm32f40_41xxx.o(.text) refers to startup_stm32f40_41xxx.o(HEAP) for Heap_Mem
    startup_stm32f40_41xxx.o(.text) refers to startup_stm32f40_41xxx.o(STACK) for Stack_Mem
    stm32f4xx_adc.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB2PeriphResetCmd
    stm32f4xx_can.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB1PeriphResetCmd
    stm32f4xx_cryp.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB2PeriphResetCmd
    stm32f4xx_cryp_aes.o(.text) refers to stm32f4xx_cryp.o(.text) for CRYP_KeyStructInit
    stm32f4xx_cryp_des.o(.text) refers to stm32f4xx_cryp.o(.text) for CRYP_KeyStructInit
    stm32f4xx_cryp_tdes.o(.text) refers to stm32f4xx_cryp.o(.text) for CRYP_KeyStructInit
    stm32f4xx_dac.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB1PeriphResetCmd
    stm32f4xx_dma2d.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB1PeriphResetCmd
    stm32f4xx_fsmc.o(.text) refers to stm32f4xx_fsmc.o(.constdata) for FSMC_DefaultTimingStruct
    stm32f4xx_gpio.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB1PeriphResetCmd
    stm32f4xx_hash.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB2PeriphResetCmd
    stm32f4xx_hash_md5.o(.text) refers to stm32f4xx_hash.o(.text) for HASH_DeInit
    stm32f4xx_hash_sha1.o(.text) refers to stm32f4xx_hash.o(.text) for HASH_DeInit
    stm32f4xx_i2c.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB1PeriphResetCmd
    stm32f4xx_ltdc.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB2PeriphResetCmd
    stm32f4xx_pwr.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB1PeriphResetCmd
    stm32f4xx_rcc.o(.text) refers to stm32f4xx_rcc.o(.data) for APBAHBPrescTable
    stm32f4xx_rng.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB2PeriphResetCmd
    stm32f4xx_sai.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB2PeriphResetCmd
    stm32f4xx_sdio.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB2PeriphResetCmd
    stm32f4xx_spi.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB2PeriphResetCmd
    stm32f4xx_syscfg.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB2PeriphResetCmd
    stm32f4xx_tim.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB2PeriphResetCmd
    stm32f4xx_usart.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB2PeriphResetCmd
    stm32f4xx_wwdg.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB1PeriphResetCmd
    delay.o(.text) refers to misc.o(.text) for SysTick_CLKSourceConfig
    delay.o(.text) refers to delay.o(.data) for fac_us
    usart.o(.rev16_text) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(.revsh_text) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(.text) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB1PeriphClockCmd
    usart.o(.text) refers to stm32f4xx_gpio.o(.text) for GPIO_PinAFConfig
    usart.o(.text) refers to stm32f4xx_usart.o(.text) for USART_Init
    usart.o(.text) refers to misc.o(.text) for NVIC_Init
    usart.o(.text) refers to usart.o(.data) for Res
    usart.o(.text) refers to usart.o(.bss) for USART_RX_BUF
    usart.o(.bss) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(.data) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    stm32f4_key.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB1PeriphClockCmd
    stm32f4_key.o(.text) refers to stm32f4xx_gpio.o(.text) for GPIO_Init
    led.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB1PeriphClockCmd
    led.o(.text) refers to stm32f4xx_gpio.o(.text) for GPIO_Init
    g.o(.text) refers to stm32f4xx_usart.o(.text) for USART_SendData
    g.o(.text) refers to delay.o(.text) for delay_ms
    g.o(.text) refers to g.o(.data) for beep
    timer.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB1PeriphClockCmd
    timer.o(.text) refers to stm32f4xx_tim.o(.text) for TIM_TimeBaseInit
    timer.o(.text) refers to misc.o(.text) for NVIC_Init
    timer.o(.text) refers to dac.o(.text) for DAC_UpdateSineOutput
    timer.o(.text) refers to main.o(.text) for Sweep_Update
    timer.o(.text) refers to usart.o(.data) for Res
    timer.o(.text) refers to timer.o(.data) for KAISHI
    kalman.o(.text) refers to kalman.o(.data) for current
    kalman.o(.text) refers to kalman.o(.bss) for state
    adc.o(.text) refers to fft.o(.text) for Hanningwindow
    adc.o(.text) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    adc.o(.text) refers to atan2.o(i.__hardfp_atan2) for __hardfp_atan2
    adc.o(.text) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    adc.o(.text) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    adc.o(.text) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    adc.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB1PeriphClockCmd
    adc.o(.text) refers to stm32f4xx_gpio.o(.text) for GPIO_Init
    adc.o(.text) refers to stm32f4xx_adc.o(.text) for ADC_CommonInit
    adc.o(.text) refers to misc.o(.text) for NVIC_Init
    adc.o(.text) refers to stm32f4xx_dma.o(.text) for DMA_DeInit
    adc.o(.text) refers to stm32f4xx_tim.o(.text) for TIM_Cmd
    adc.o(.text) refers to fft.o(.data) for peak1_idx
    adc.o(.text) refers to fft.o(.bss) for fft_inputbuf
    adc.o(.text) refers to adc.o(.data) for phase_A
    adc.o(.text) refers to adc.o(.bss) for buff_adc
    adc.o(.text) refers to main.o(.data) for adc1_sample_index
    adc.o(.text) refers to main.o(.bss) for adc1_sample_buffer
    fft.o(.text) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    fft.o(.text) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    fft.o(.text) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    fft.o(.text) refers to arm_cfft_radix4_f32.o(.text) for arm_cfft_radix4_f32
    fft.o(.text) refers to arm_cmplx_mag_f32.o(.text) for arm_cmplx_mag_f32
    fft.o(.text) refers to cos.o(i.__hardfp_cos) for __hardfp_cos
    fft.o(.text) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    fft.o(.text) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    fft.o(.text) refers to sin.o(i.__hardfp_sin) for __hardfp_sin
    fft.o(.text) refers to fft.o(.data) for i
    fft.o(.text) refers to fft.o(.bss) for fft_inputbuf
    fft.o(.text) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    fft.o(.text) refers to sqrt.o(i.__hardfp_sqrt) for __hardfp_sqrt
    fft.o(.text) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    fft.o(.text) refers to kalman.o(.text) for kalman_thd
    lcd.o(.text) refers to lcd.o(.bss) for lcddev
    lcd.o(.text) refers to dflt_clz.o(x$fpl$dfltu) for __aeabi_ui2d
    lcd.o(.text) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    lcd.o(.text) refers to dfixu.o(x$fpl$dfixu) for __aeabi_d2uiz
    lcd.o(.text) refers to sys.o(.text) for sys_gpio_pin_set
    lcd.o(.text) refers to delay.o(.text) for delay_ms
    lcd.o(.text) refers to lcd_ex.o(.text) for lcd_ex_st7789_reginit
    lcd.o(.text) refers to lcd.o(.constdata) for asc2_1206
    lcd.o(.text) refers to lcd.o(.data) for g_back_color
    lcd_ex.o(.text) refers to lcd.o(.text) for lcd_wr_regno
    lcd_ex.o(.text) refers to delay.o(.text) for delay_ms
    ad9833.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB1PeriphClockCmd
    ad9833.o(.text) refers to stm32f4xx_gpio.o(.text) for GPIO_Init
    ad9833.o(.text) refers to delay.o(.text) for delay_ms
    ad9833.o(.text) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    ad9833.o(.text) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    ad9833.o(.text) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    ad9833.o(.text) refers to dfixu.o(x$fpl$dfixu) for __aeabi_d2uiz
    ad9833.o(.text) refers to ad9833.o(.constdata) for .constdata
    spi.o(.text) refers to stm32f4xx_spi.o(.text) for SPI_I2S_GetFlagStatus
    spi.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB2PeriphClockCmd
    spi.o(.text) refers to stm32f4xx_gpio.o(.text) for GPIO_Init
    dac.o(.text) refers to stm32f4xx_dac.o(.text) for DAC_SetChannel1Data
    dac.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB1PeriphClockCmd
    dac.o(.text) refers to stm32f4xx_gpio.o(.text) for GPIO_Init
    dac.o(.text) refers to sinf.o(i.__hardfp_sinf) for __hardfp_sinf
    dac.o(.text) refers to dac.o(.bss) for sine_table
    dac.o(.text) refers to dac.o(.data) for dac_output_enabled
    dac.o(.text) refers to dac.o(.constdata) for amplitude_table
    dac.o(.text) refers to delay.o(.text) for delay_ms
    touch_1.o(.text) refers to sys.o(.text) for sys_gpio_set
    touch_1.o(.text) refers to delay.o(.text) for delay_us
    touch_1.o(.text) refers to touch_1.o(.data) for t
    touch_1.o(.text) refers to lcd.o(.bss) for lcddev
    touch_1.o(.text) refers to lcd.o(.text) for lcd_show_string
    touch_1.o(.data) refers to touch_1.o(.text) for tp_init
    arm_cfft_radix4_f32.o(.text) refers to arm_bitreversal.o(.text) for arm_bitreversal_f32
    arm_cfft_radix4_init_f32.o(.text) refers to arm_common_tables.o(.constdata) for twiddleCoef_4096
    arm_cfft_radix4_init_f32.o(.text) refers to arm_common_tables.o(.constdata) for armBitRevTable
    use_no_semi_2.o(.text) refers (Special) to use_no_semi.o(.text) for __use_no_semihosting_swi
    __2printf.o(.text) refers to _printf_char_file.o(.text) for _printf_char_file
    __2printf.o(.text) refers to usart.o(.data) for __stdout
    __2sprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    __2sprintf.o(.text) refers to _sputc.o(.text) for _sputc
    noretval__2printf.o(.text) refers to _printf_char_file.o(.text) for _printf_char_file
    noretval__2printf.o(.text) refers to usart.o(.data) for __stdout
    noretval__2sprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    noretval__2sprintf.o(.text) refers to _sputc.o(.text) for _sputc
    __printf.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    _printf_str.o(.text) refers (Special) to _printf_char.o(.text) for _printf_cs_common
    _printf_str.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_str.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll.o(.text) refers to _printf_hex_ll.o(.constdata) for .constdata
    _printf_hex_int.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int.o(.text) refers to _printf_hex_int.o(.constdata) for .constdata
    _printf_hex_int_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll.o(.text) refers to _printf_hex_int_ll.o(.constdata) for .constdata
    _printf_hex_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ptr.o(.text) refers to _printf_hex_ptr.o(.constdata) for .constdata
    _printf_hex_int_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ptr.o(.text) refers to _printf_hex_int_ptr.o(.constdata) for .constdata
    _printf_hex_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll_ptr.o(.text) refers to _printf_hex_ll_ptr.o(.constdata) for .constdata
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_hex_int_ll_ptr.o(.constdata) for .constdata
    __printf_flags.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags.o(.text) refers to __printf_flags.o(.constdata) for .constdata
    __printf_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to __printf_flags_ss.o(.constdata) for .constdata
    __printf_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_flags_wp.o(.constdata) for .constdata
    __printf_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_flags_ss_wp.o(.constdata) for .constdata
    _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) refers (Weak) to _printf_char.o(.text) for _printf_string
    _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) refers (Weak) to _printf_hex_int.o(.text) for _printf_int_hex
    _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) refers (Special) to _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017) for _printf_percent_end
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    d2f.o(x$fpl$d2f) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    d2f.o(x$fpl$d2f) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    d2f.o(x$fpl$d2f) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    daddsub_clz.o(x$fpl$dadd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$dadd) refers to daddsub_clz.o(x$fpl$dsub) for _dsub1
    daddsub_clz.o(x$fpl$dadd) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    daddsub_clz.o(x$fpl$dadd) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    daddsub_clz.o(x$fpl$drsb) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$drsb) refers to daddsub_clz.o(x$fpl$dadd) for _dadd1
    daddsub_clz.o(x$fpl$drsb) refers to daddsub_clz.o(x$fpl$dsub) for _dsub1
    daddsub_clz.o(x$fpl$dsub) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$dsub) refers to daddsub_clz.o(x$fpl$dadd) for _dadd1
    daddsub_clz.o(x$fpl$dsub) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    ddiv.o(x$fpl$drdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$drdiv) refers to ddiv.o(x$fpl$ddiv) for ddiv_entry
    ddiv.o(x$fpl$ddiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$ddiv) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    ddiv.o(x$fpl$ddiv) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfixu.o(x$fpl$dfixu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfixu.o(x$fpl$dfixu) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfixu.o(x$fpl$dfixur) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfixu.o(x$fpl$dfixur) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dflt_clz.o(x$fpl$dfltu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dflt_clz.o(x$fpl$dflt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dflt_clz.o(x$fpl$dfltn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dmul.o(x$fpl$dmul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dmul.o(x$fpl$dmul) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    dmul.o(x$fpl$dmul) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    f2d.o(x$fpl$f2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    f2d.o(x$fpl$f2d) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    f2d.o(x$fpl$f2d) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    printf1.o(x$fpl$printf1) refers to _printf_fp_dec.o(.text) for _printf_fp_dec_real
    atan2.o(i.__hardfp_atan2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2.o(i.__hardfp_atan2) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    atan2.o(i.__hardfp_atan2) refers to atan.o(i.atan) for atan
    atan2.o(i.__hardfp_atan2) refers to basic.o(x$fpl$basic) for __aeabi_dneg
    atan2.o(i.__hardfp_atan2) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    atan2.o(i.__hardfp_atan2) refers to fabs.o(i.fabs) for fabs
    atan2.o(i.__hardfp_atan2) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    atan2.o(i.__hardfp_atan2) refers to deqf.o(x$fpl$deqf) for __aeabi_cdcmpeq
    atan2.o(i.__hardfp_atan2) refers to _rserrno.o(.text) for __set_errno
    atan2.o(i.__hardfp_atan2) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    atan2.o(i.__hardfp_atan2) refers to qnan.o(.constdata) for __mathlib_zero
    atan2.o(i.__softfp_atan2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2.o(i.__softfp_atan2) refers to atan2.o(i.__hardfp_atan2) for __hardfp_atan2
    atan2.o(i.atan2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2.o(i.atan2) refers to atan2.o(i.__hardfp_atan2) for __hardfp_atan2
    atan2_x.o(i.____hardfp_atan2$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2_x.o(i.____hardfp_atan2$lsc) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    atan2_x.o(i.____hardfp_atan2$lsc) refers to atan.o(i.atan) for atan
    atan2_x.o(i.____hardfp_atan2$lsc) refers to _rserrno.o(.text) for __set_errno
    atan2_x.o(i.____hardfp_atan2$lsc) refers to basic.o(x$fpl$basic) for __aeabi_dneg
    atan2_x.o(i.____hardfp_atan2$lsc) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    atan2_x.o(i.____hardfp_atan2$lsc) refers to fabs.o(i.fabs) for fabs
    atan2_x.o(i.____hardfp_atan2$lsc) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    atan2_x.o(i.____hardfp_atan2$lsc) refers to deqf.o(x$fpl$deqf) for __aeabi_cdcmpeq
    atan2_x.o(i.____hardfp_atan2$lsc) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    atan2_x.o(i.____hardfp_atan2$lsc) refers to qnan.o(.constdata) for __mathlib_zero
    atan2_x.o(i.____softfp_atan2$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2_x.o(i.____softfp_atan2$lsc) refers to atan2_x.o(i.____hardfp_atan2$lsc) for ____hardfp_atan2$lsc
    atan2_x.o(i.__atan2$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2_x.o(i.__atan2$lsc) refers to atan2_x.o(i.____hardfp_atan2$lsc) for ____hardfp_atan2$lsc
    atan2f.o(i.__hardfp_atan2f) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2f.o(i.__hardfp_atan2f) refers to fpclassifyf.o(i.__ARM_fpclassifyf) for __ARM_fpclassifyf
    atan2f.o(i.__hardfp_atan2f) refers to funder.o(i.__mathlib_flt_underflow) for __mathlib_flt_underflow
    atan2f.o(i.__hardfp_atan2f) refers to _rserrno.o(.text) for __set_errno
    atan2f.o(i.__hardfp_atan2f) refers to funder.o(i.__mathlib_flt_infnan2) for __mathlib_flt_infnan2
    atan2f.o(i.__softfp_atan2f) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2f.o(i.__softfp_atan2f) refers to atan2f.o(i.__hardfp_atan2f) for __hardfp_atan2f
    atan2f.o(i.atan2f) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2f.o(i.atan2f) refers to atan2f.o(i.__hardfp_atan2f) for __hardfp_atan2f
    atan2f_x.o(i.____hardfp_atan2f$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2f_x.o(i.____hardfp_atan2f$lsc) refers to fpclassifyf.o(i.__ARM_fpclassifyf) for __ARM_fpclassifyf
    atan2f_x.o(i.____hardfp_atan2f$lsc) refers to _rserrno.o(.text) for __set_errno
    atan2f_x.o(i.____hardfp_atan2f$lsc) refers to funder.o(i.__mathlib_flt_infnan2) for __mathlib_flt_infnan2
    atan2f_x.o(i.____softfp_atan2f$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2f_x.o(i.____softfp_atan2f$lsc) refers to atan2f_x.o(i.____hardfp_atan2f$lsc) for ____hardfp_atan2f$lsc
    atan2f_x.o(i.__atan2f$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2f_x.o(i.__atan2f$lsc) refers to atan2f_x.o(i.____hardfp_atan2f$lsc) for ____hardfp_atan2f$lsc
    cos.o(i.__hardfp_cos) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    cos.o(i.__hardfp_cos) refers to _rserrno.o(.text) for __set_errno
    cos.o(i.__hardfp_cos) refers to dunder.o(i.__mathlib_dbl_invalid) for __mathlib_dbl_invalid
    cos.o(i.__hardfp_cos) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    cos.o(i.__hardfp_cos) refers to rred.o(i.__ieee754_rem_pio2) for __ieee754_rem_pio2
    cos.o(i.__hardfp_cos) refers to sin_i.o(i.__kernel_sin) for __kernel_sin
    cos.o(i.__hardfp_cos) refers to cos_i.o(i.__kernel_cos) for __kernel_cos
    cos.o(i.__hardfp_cos) refers to basic.o(x$fpl$basic) for __aeabi_dneg
    cos.o(i.__softfp_cos) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    cos.o(i.__softfp_cos) refers to cos.o(i.__hardfp_cos) for __hardfp_cos
    cos.o(i.cos) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    cos.o(i.cos) refers to cos.o(i.__hardfp_cos) for __hardfp_cos
    cos_x.o(i.____hardfp_cos$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    cos_x.o(i.____hardfp_cos$lsc) refers to _rserrno.o(.text) for __set_errno
    cos_x.o(i.____hardfp_cos$lsc) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    cos_x.o(i.____hardfp_cos$lsc) refers to rred.o(i.__ieee754_rem_pio2) for __ieee754_rem_pio2
    cos_x.o(i.____hardfp_cos$lsc) refers to sin_i.o(i.__kernel_sin) for __kernel_sin
    cos_x.o(i.____hardfp_cos$lsc) refers to cos_i.o(i.__kernel_cos) for __kernel_cos
    cos_x.o(i.____hardfp_cos$lsc) refers to basic.o(x$fpl$basic) for __aeabi_dneg
    cos_x.o(i.____softfp_cos$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    cos_x.o(i.____softfp_cos$lsc) refers to cos_x.o(i.____hardfp_cos$lsc) for ____hardfp_cos$lsc
    cos_x.o(i.__cos$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    cos_x.o(i.__cos$lsc) refers to cos_x.o(i.____hardfp_cos$lsc) for ____hardfp_cos$lsc
    sin.o(i.__hardfp_sin) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sin.o(i.__hardfp_sin) refers to _rserrno.o(.text) for __set_errno
    sin.o(i.__hardfp_sin) refers to dunder.o(i.__mathlib_dbl_invalid) for __mathlib_dbl_invalid
    sin.o(i.__hardfp_sin) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    sin.o(i.__hardfp_sin) refers to rred.o(i.__ieee754_rem_pio2) for __ieee754_rem_pio2
    sin.o(i.__hardfp_sin) refers to cos_i.o(i.__kernel_cos) for __kernel_cos
    sin.o(i.__hardfp_sin) refers to basic.o(x$fpl$basic) for __aeabi_dneg
    sin.o(i.__hardfp_sin) refers to sin_i.o(i.__kernel_sin) for __kernel_sin
    sin.o(i.__softfp_sin) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sin.o(i.__softfp_sin) refers to sin.o(i.__hardfp_sin) for __hardfp_sin
    sin.o(i.sin) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sin.o(i.sin) refers to sin.o(i.__hardfp_sin) for __hardfp_sin
    sin_x.o(i.____hardfp_sin$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sin_x.o(i.____hardfp_sin$lsc) refers to _rserrno.o(.text) for __set_errno
    sin_x.o(i.____hardfp_sin$lsc) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    sin_x.o(i.____hardfp_sin$lsc) refers to rred.o(i.__ieee754_rem_pio2) for __ieee754_rem_pio2
    sin_x.o(i.____hardfp_sin$lsc) refers to cos_i.o(i.__kernel_cos) for __kernel_cos
    sin_x.o(i.____hardfp_sin$lsc) refers to basic.o(x$fpl$basic) for __aeabi_dneg
    sin_x.o(i.____hardfp_sin$lsc) refers to sin_i.o(i.__kernel_sin) for __kernel_sin
    sin_x.o(i.____softfp_sin$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sin_x.o(i.____softfp_sin$lsc) refers to sin_x.o(i.____hardfp_sin$lsc) for ____hardfp_sin$lsc
    sin_x.o(i.__sin$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sin_x.o(i.__sin$lsc) refers to sin_x.o(i.____hardfp_sin$lsc) for ____hardfp_sin$lsc
    sinf.o(i.__hardfp_sinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sinf.o(i.__hardfp_sinf) refers to rredf.o(i.__mathlib_rredf2) for __mathlib_rredf2
    sinf.o(i.__hardfp_sinf) refers to fpclassifyf.o(i.__ARM_fpclassifyf) for __ARM_fpclassifyf
    sinf.o(i.__hardfp_sinf) refers to funder.o(i.__mathlib_flt_underflow) for __mathlib_flt_underflow
    sinf.o(i.__hardfp_sinf) refers to _rserrno.o(.text) for __set_errno
    sinf.o(i.__hardfp_sinf) refers to funder.o(i.__mathlib_flt_invalid) for __mathlib_flt_invalid
    sinf.o(i.__hardfp_sinf) refers to funder.o(i.__mathlib_flt_infnan) for __mathlib_flt_infnan
    sinf.o(i.__softfp_sinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sinf.o(i.__softfp_sinf) refers to sinf.o(i.__hardfp_sinf) for __hardfp_sinf
    sinf.o(i.sinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sinf.o(i.sinf) refers to sinf.o(i.__hardfp_sinf) for __hardfp_sinf
    sinf_x.o(i.____hardfp_sinf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sinf_x.o(i.____hardfp_sinf$lsc) refers to rredf.o(i.__mathlib_rredf2) for __mathlib_rredf2
    sinf_x.o(i.____hardfp_sinf$lsc) refers to _rserrno.o(.text) for __set_errno
    sinf_x.o(i.____hardfp_sinf$lsc) refers to funder.o(i.__mathlib_flt_infnan) for __mathlib_flt_infnan
    sinf_x.o(i.____softfp_sinf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sinf_x.o(i.____softfp_sinf$lsc) refers to sinf_x.o(i.____hardfp_sinf$lsc) for ____hardfp_sinf$lsc
    sinf_x.o(i.__sinf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sinf_x.o(i.__sinf$lsc) refers to sinf_x.o(i.____hardfp_sinf$lsc) for ____hardfp_sinf$lsc
    sqrt.o(i.__hardfp_sqrt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt.o(i.__hardfp_sqrt) refers to dsqrt_umaal.o(x$fpl$dsqrt) for _dsqrt
    sqrt.o(i.__hardfp_sqrt) refers to _rserrno.o(.text) for __set_errno
    sqrt.o(i.__softfp_sqrt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt.o(i.__softfp_sqrt) refers to dsqrt_umaal.o(x$fpl$dsqrt) for _dsqrt
    sqrt.o(i.__softfp_sqrt) refers to _rserrno.o(.text) for __set_errno
    sqrt.o(i.sqrt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt.o(i.sqrt) refers to dsqrt_umaal.o(x$fpl$dsqrt) for _dsqrt
    sqrt.o(i.sqrt) refers to _rserrno.o(.text) for __set_errno
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers to _rserrno.o(.text) for __set_errno
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers to dsqrt_umaal.o(x$fpl$dsqrt) for _dsqrt
    sqrt_x.o(i.____softfp_sqrt$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to _rserrno.o(.text) for __set_errno
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to dsqrt_umaal.o(x$fpl$dsqrt) for _dsqrt
    sqrt_x.o(i.__sqrt$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt_x.o(i.__sqrt$lsc) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    sqrt_x.o(i.__sqrt$lsc) refers to _rserrno.o(.text) for __set_errno
    sqrt_x.o(i.__sqrt$lsc) refers to dsqrt_umaal.o(x$fpl$dsqrt) for _dsqrt
    sqrtf.o(i.__hardfp_sqrtf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrtf.o(i.__hardfp_sqrtf) refers to _rserrno.o(.text) for __set_errno
    sqrtf.o(i.__softfp_sqrtf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrtf.o(i.__softfp_sqrtf) refers to _rserrno.o(.text) for __set_errno
    sqrtf.o(i.sqrtf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrtf.o(i.sqrtf) refers to _rserrno.o(.text) for __set_errno
    sqrtf_x.o(i.____hardfp_sqrtf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrtf_x.o(i.____hardfp_sqrtf$lsc) refers to _rserrno.o(.text) for __set_errno
    sqrtf_x.o(i.____softfp_sqrtf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrtf_x.o(i.____softfp_sqrtf$lsc) refers to _rserrno.o(.text) for __set_errno
    sqrtf_x.o(i.__sqrtf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrtf_x.o(i.__sqrtf$lsc) refers to _rserrno.o(.text) for __set_errno
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    _rserrno.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_dec.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_dec.o(.text) refers (Special) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    _printf_fp_dec.o(.text) refers to bigflt0.o(.text) for _btod_etento
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_d2e) for _btod_d2e
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    _printf_fp_dec.o(.text) refers to lludiv10.o(.text) for _ll_udiv10
    _printf_fp_dec.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_dec.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_char_common.o(.text) refers to __printf_flags_wp.o(.text) for __printf
    _printf_char.o(.text) refers (Weak) to _printf_str.o(.text) for _printf_str
    _printf_char_file.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    _printf_char_file.o(.text) refers to ferror.o(.text) for ferror
    _printf_char_file.o(.text) refers to usart.o(.text) for fputc
    basic.o(x$fpl$basic) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    deqf.o(x$fpl$deqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    deqf.o(x$fpl$deqf) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    deqf.o(x$fpl$deqf) refers to dcmpi.o(x$fpl$dcmpinf) for __fpl_dcmp_Inf
    dleqf.o(x$fpl$dleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dleqf.o(x$fpl$dleqf) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dleqf.o(x$fpl$dleqf) refers to dcmpi.o(x$fpl$dcmpinf) for __fpl_dcmp_Inf
    dnaninf.o(x$fpl$dnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dretinf.o(x$fpl$dretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dsqrt_umaal.o(x$fpl$dsqrt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dsqrt_umaal.o(x$fpl$dsqrt) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    fnaninf.o(x$fpl$fnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fretinf.o(x$fpl$fretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan.o(i.__hardfp_atan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan.o(i.__hardfp_atan) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    atan.o(i.__hardfp_atan) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    atan.o(i.__hardfp_atan) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    atan.o(i.__hardfp_atan) refers to fabs.o(i.fabs) for fabs
    atan.o(i.__hardfp_atan) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    atan.o(i.__hardfp_atan) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    atan.o(i.__hardfp_atan) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    atan.o(i.__hardfp_atan) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    atan.o(i.__hardfp_atan) refers to poly.o(i.__kernel_poly) for __kernel_poly
    atan.o(i.__hardfp_atan) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    atan.o(i.__hardfp_atan) refers to basic.o(x$fpl$basic) for __aeabi_dneg
    atan.o(i.__hardfp_atan) refers to atan.o(.constdata) for .constdata
    atan.o(i.__softfp_atan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan.o(i.__softfp_atan) refers to atan.o(i.__hardfp_atan) for __hardfp_atan
    atan.o(i.atan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan.o(i.atan) refers to atan.o(i.__hardfp_atan) for __hardfp_atan
    atan.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan_x.o(i.____hardfp_atan$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan_x.o(i.____hardfp_atan$lsc) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    atan_x.o(i.____hardfp_atan$lsc) refers to fabs.o(i.fabs) for fabs
    atan_x.o(i.____hardfp_atan$lsc) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    atan_x.o(i.____hardfp_atan$lsc) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    atan_x.o(i.____hardfp_atan$lsc) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    atan_x.o(i.____hardfp_atan$lsc) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    atan_x.o(i.____hardfp_atan$lsc) refers to poly.o(i.__kernel_poly) for __kernel_poly
    atan_x.o(i.____hardfp_atan$lsc) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    atan_x.o(i.____hardfp_atan$lsc) refers to basic.o(x$fpl$basic) for __aeabi_dneg
    atan_x.o(i.____hardfp_atan$lsc) refers to atan_x.o(.constdata) for .constdata
    atan_x.o(i.____softfp_atan$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan_x.o(i.____softfp_atan$lsc) refers to atan_x.o(i.____hardfp_atan$lsc) for ____hardfp_atan$lsc
    atan_x.o(i.__atan$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan_x.o(i.__atan$lsc) refers to atan_x.o(i.____hardfp_atan$lsc) for ____hardfp_atan$lsc
    atan_x.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    cos_i.o(i.__kernel_cos) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    cos_i.o(i.__kernel_cos) refers to dfix.o(x$fpl$dfix) for __aeabi_d2iz
    cos_i.o(i.__kernel_cos) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    cos_i.o(i.__kernel_cos) refers to poly.o(i.__kernel_poly) for __kernel_poly
    cos_i.o(i.__kernel_cos) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    cos_i.o(i.__kernel_cos) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    cos_i.o(i.__kernel_cos) refers to cos_i.o(.constdata) for .constdata
    cos_i.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dunder.o(i.__mathlib_dbl_divzero) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_infnan) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_infnan2) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_invalid) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_overflow) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_posinfnan) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_underflow) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    fabs.o(i.__hardfp_fabs) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fabs.o(i.__softfp_fabs) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fabs.o(i.fabs) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fpclassifyf.o(i.__ARM_fpclassifyf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    qnan.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    rred.o(i.__ieee754_rem_pio2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    rred.o(i.__ieee754_rem_pio2) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    rred.o(i.__ieee754_rem_pio2) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    rred.o(i.__ieee754_rem_pio2) refers to fabs.o(i.fabs) for fabs
    rred.o(i.__ieee754_rem_pio2) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    rred.o(i.__ieee754_rem_pio2) refers to dfix.o(x$fpl$dfix) for __aeabi_d2iz
    rred.o(i.__ieee754_rem_pio2) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    rred.o(i.__ieee754_rem_pio2) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    rred.o(i.__ieee754_rem_pio2) refers to basic.o(x$fpl$basic) for __aeabi_dneg
    rred.o(i.__ieee754_rem_pio2) refers to dflt_clz.o(x$fpl$dfltu) for __aeabi_ui2d
    rred.o(i.__ieee754_rem_pio2) refers to rred.o(.constdata) for .constdata
    rred.o(i.__use_accurate_range_reduction) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    rred.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    rredf.o(i.__mathlib_rredf2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    rredf.o(i.__mathlib_rredf2) refers to rredf.o(.constdata) for .constdata
    rredf.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sin_i.o(i.__kernel_sin) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sin_i.o(i.__kernel_sin) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    sin_i.o(i.__kernel_sin) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    sin_i.o(i.__kernel_sin) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    sin_i.o(i.__kernel_sin) refers to poly.o(i.__kernel_poly) for __kernel_poly
    sin_i.o(i.__kernel_sin) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    sin_i.o(i.__kernel_sin) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    sin_i.o(i.__kernel_sin) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    sin_i.o(i.__kernel_sin) refers to sin_i.o(.constdata) for .constdata
    sin_i.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sin_i_x.o(i.____kernel_sin$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sin_i_x.o(i.____kernel_sin$lsc) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    sin_i_x.o(i.____kernel_sin$lsc) refers to poly.o(i.__kernel_poly) for __kernel_poly
    sin_i_x.o(i.____kernel_sin$lsc) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    sin_i_x.o(i.____kernel_sin$lsc) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    sin_i_x.o(i.____kernel_sin$lsc) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    sin_i_x.o(i.____kernel_sin$lsc) refers to sin_i_x.o(.constdata) for .constdata
    sin_i_x.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to main.o(.text) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    rt_locale.o(.text) refers to rt_locale.o(.bss) for __rt_locale_data
    rt_locale_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    rt_errno_addr.o(.text) refers to rt_errno_addr.o(.bss) for __aeabi_errno_addr_data
    rt_errno_addr_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    _printf_fp_infnan.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_infnan.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    bigflt0.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    bigflt0.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    bigflt0.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    bigflt0.o(.text) refers to bigflt0.o(.constdata) for .constdata
    bigflt0.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers to btod.o(CL$$btod_d2e_norm_op1) for _d2e_norm_op1
    btod.o(CL$$btod_d2e_norm_op1) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e_norm_op1) refers to btod.o(CL$$btod_d2e_denorm_low) for _d2e_denorm_low
    btod.o(CL$$btod_d2e_denorm_low) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_ediv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_emuld) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_edivd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_e2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_mult_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_div_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    lc_numeric_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000016) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000016) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers to strcmpv7m.o(.text) for strcmp
    lc_numeric_c.o(locale$$code) refers to lc_numeric_c.o(locale$$data) for __lcnum_c_name
    dcmpi.o(x$fpl$dcmpinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfix.o(x$fpl$dfix) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfix.o(x$fpl$dfix) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfix.o(x$fpl$dfixr) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfix.o(x$fpl$dfixr) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    istatus.o(x$fpl$ieeestatus) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    poly.o(i.__kernel_poly) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    poly.o(i.__kernel_poly) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    poly.o(i.__kernel_poly) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_stm32f40_41xxx.o(.text) for __user_initial_stackheap
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002C) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000001) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000A) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000011) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000033) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_user_alloc_1
    libinit2.o(.ARM.Collect$$libinit$$00000001) refers to fpinit.o(x$fpl$fpinit) for _fp_init
    libinit2.o(.ARM.Collect$$libinit$$0000000F) refers (Weak) to rt_locale_intlibspace.o(.text) for __rt_locale
    libinit2.o(.ARM.Collect$$libinit$$00000010) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers (Weak) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000026) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000027) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    argv_veneer.o(.emb_text) refers to no_argv.o(.text) for __ARM_get_argv
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to usart.o(.text) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000004) for __rt_lib_shutdown_cpp_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000002) for __rt_lib_shutdown_fini_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000009) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000011) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000012) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000006) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E) for __rt_lib_shutdown_user_alloc_1
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to usart.o(.text) for _sys_exit
    defsig_exit.o(.text) refers to usart.o(.text) for _sys_exit
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display


==============================================================================

Removing Unused input sections from the image.

    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_it.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_it.o(.revsh_text), (4 bytes).
    Removing system_stm32f4xx.o(.rev16_text), (4 bytes).
    Removing system_stm32f4xx.o(.revsh_text), (4 bytes).
    Removing misc.o(.rev16_text), (4 bytes).
    Removing misc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_adc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_adc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_can.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_can.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_can.o(.text), (2544 bytes).
    Removing stm32f4xx_crc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_crc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_crc.o(.text), (72 bytes).
    Removing stm32f4xx_cryp.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_cryp.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_cryp.o(.text), (856 bytes).
    Removing stm32f4xx_cryp_aes.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_cryp_aes.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_cryp_aes.o(.text), (4586 bytes).
    Removing stm32f4xx_cryp_des.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_cryp_des.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_cryp_des.o(.text), (472 bytes).
    Removing stm32f4xx_cryp_tdes.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_cryp_tdes.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_cryp_tdes.o(.text), (536 bytes).
    Removing stm32f4xx_dac.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_dac.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_dbgmcu.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_dbgmcu.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_dbgmcu.o(.text), (100 bytes).
    Removing stm32f4xx_dcmi.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_dcmi.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_dcmi.o(.text), (396 bytes).
    Removing stm32f4xx_dma2d.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_dma2d.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_dma2d.o(.text), (948 bytes).
    Removing stm32f4xx_dma.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_dma.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_exti.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_exti.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_exti.o(.text), (272 bytes).
    Removing stm32f4xx_flash.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_flash.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_flash.o(.text), (1684 bytes).
    Removing stm32f4xx_flash_ramfunc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_flash_ramfunc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_flash_ramfunc.o(.text), (64 bytes).
    Removing stm32f4xx_fsmc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_fsmc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_fsmc.o(.text), (1480 bytes).
    Removing stm32f4xx_fsmc.o(.constdata), (28 bytes).
    Removing stm32f4xx_gpio.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_gpio.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hash.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hash.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hash.o(.text), (552 bytes).
    Removing stm32f4xx_hash_md5.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hash_md5.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hash_md5.o(.text), (534 bytes).
    Removing stm32f4xx_hash_sha1.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hash_sha1.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hash_sha1.o(.text), (548 bytes).
    Removing stm32f4xx_i2c.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_i2c.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_i2c.o(.text), (1110 bytes).
    Removing stm32f4xx_iwdg.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_iwdg.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_iwdg.o(.text), (64 bytes).
    Removing stm32f4xx_ltdc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_ltdc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_ltdc.o(.text), (1672 bytes).
    Removing stm32f4xx_pwr.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_pwr.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_pwr.o(.text), (364 bytes).
    Removing stm32f4xx_rcc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_rcc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_rng.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_rng.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_rng.o(.text), (160 bytes).
    Removing stm32f4xx_rtc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_rtc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_rtc.o(.text), (3432 bytes).
    Removing stm32f4xx_sai.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_sai.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_sai.o(.text), (524 bytes).
    Removing stm32f4xx_sdio.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_sdio.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_sdio.o(.text), (476 bytes).
    Removing stm32f4xx_spi.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_spi.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_spi.o(.text), (1152 bytes).
    Removing stm32f4xx_syscfg.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_syscfg.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_syscfg.o(.text), (148 bytes).
    Removing stm32f4xx_tim.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_tim.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_usart.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_usart.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_wwdg.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_wwdg.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_wwdg.o(.text), (144 bytes).
    Removing delay.o(.rev16_text), (4 bytes).
    Removing delay.o(.revsh_text), (4 bytes).
    Removing sys.o(.rev16_text), (4 bytes).
    Removing sys.o(.revsh_text), (4 bytes).
    Removing sys.o(.emb_text), (16 bytes).
    Removing usart.o(.rev16_text), (4 bytes).
    Removing usart.o(.revsh_text), (4 bytes).
    Removing stm32f4_key.o(.rev16_text), (4 bytes).
    Removing stm32f4_key.o(.revsh_text), (4 bytes).
    Removing led.o(.rev16_text), (4 bytes).
    Removing led.o(.revsh_text), (4 bytes).
    Removing g.o(.rev16_text), (4 bytes).
    Removing g.o(.revsh_text), (4 bytes).
    Removing g.o(.text), (148 bytes).
    Removing g.o(.data), (1 bytes).
    Removing timer.o(.rev16_text), (4 bytes).
    Removing timer.o(.revsh_text), (4 bytes).
    Removing kalman.o(.rev16_text), (4 bytes).
    Removing kalman.o(.revsh_text), (4 bytes).
    Removing adc.o(.rev16_text), (4 bytes).
    Removing adc.o(.revsh_text), (4 bytes).
    Removing fft.o(.rev16_text), (4 bytes).
    Removing fft.o(.revsh_text), (4 bytes).
    Removing lcd.o(.rev16_text), (4 bytes).
    Removing lcd.o(.revsh_text), (4 bytes).
    Removing lcd_ex.o(.rev16_text), (4 bytes).
    Removing lcd_ex.o(.revsh_text), (4 bytes).
    Removing ad9833.o(.rev16_text), (4 bytes).
    Removing ad9833.o(.revsh_text), (4 bytes).
    Removing spi.o(.rev16_text), (4 bytes).
    Removing spi.o(.revsh_text), (4 bytes).
    Removing spi.o(.text), (280 bytes).
    Removing spi.o(.data), (4 bytes).
    Removing dac.o(.rev16_text), (4 bytes).
    Removing dac.o(.revsh_text), (4 bytes).
    Removing touch_1.o(.rev16_text), (4 bytes).
    Removing touch_1.o(.revsh_text), (4 bytes).
    Removing touch_1.o(.text), (1700 bytes).
    Removing touch_1.o(.data), (61 bytes).
    Removing arm_cmplx_mag_f32.o(.rev16_text), (4 bytes).
    Removing arm_cmplx_mag_f32.o(.revsh_text), (4 bytes).
    Removing arm_cfft_radix4_f32.o(.rev16_text), (4 bytes).
    Removing arm_cfft_radix4_f32.o(.revsh_text), (4 bytes).
    Removing arm_cfft_radix4_init_f32.o(.rev16_text), (4 bytes).
    Removing arm_cfft_radix4_init_f32.o(.revsh_text), (4 bytes).
    Removing arm_bitreversal.o(.rev16_text), (4 bytes).
    Removing arm_bitreversal.o(.revsh_text), (4 bytes).
    Removing arm_common_tables.o(.rev16_text), (4 bytes).
    Removing arm_common_tables.o(.revsh_text), (4 bytes).
    Removing arm_common_tables.o(.constdata), (128 bytes).
    Removing arm_common_tables.o(.constdata), (256 bytes).
    Removing arm_common_tables.o(.constdata), (512 bytes).
    Removing arm_common_tables.o(.constdata), (1024 bytes).
    Removing arm_common_tables.o(.constdata), (2048 bytes).
    Removing arm_common_tables.o(.constdata), (4096 bytes).
    Removing arm_common_tables.o(.constdata), (8192 bytes).
    Removing arm_common_tables.o(.constdata), (16384 bytes).
    Removing arm_common_tables.o(.constdata), (24576 bytes).
    Removing arm_common_tables.o(.constdata), (12288 bytes).
    Removing arm_common_tables.o(.constdata), (128 bytes).
    Removing arm_common_tables.o(.constdata), (256 bytes).
    Removing arm_common_tables.o(.constdata), (40 bytes).
    Removing arm_common_tables.o(.constdata), (96 bytes).
    Removing arm_common_tables.o(.constdata), (112 bytes).
    Removing arm_common_tables.o(.constdata), (416 bytes).
    Removing arm_common_tables.o(.constdata), (880 bytes).
    Removing arm_common_tables.o(.constdata), (896 bytes).
    Removing arm_common_tables.o(.constdata), (3600 bytes).
    Removing arm_common_tables.o(.constdata), (7616 bytes).
    Removing arm_common_tables.o(.constdata), (8064 bytes).
    Removing arm_common_tables.o(.constdata), (128 bytes).
    Removing arm_common_tables.o(.constdata), (256 bytes).
    Removing arm_common_tables.o(.constdata), (512 bytes).
    Removing arm_common_tables.o(.constdata), (1024 bytes).
    Removing arm_common_tables.o(.constdata), (2048 bytes).
    Removing arm_common_tables.o(.constdata), (4096 bytes).
    Removing arm_common_tables.o(.constdata), (8192 bytes).
    Removing arm_common_tables.o(.constdata), (16384 bytes).

182 unused section(s) (total 151848 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/dczerorl2.s                0x00000000   Number         0  __dczerorl2.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr_intlibspace.o ABSOLUTE
    ../clib/angel/scatter.s                  0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi_2.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/bigflt.c                         0x00000000   Number         0  bigflt0.o ABSOLUTE
    ../clib/btod.s                           0x00000000   Number         0  btod.o ABSOLUTE
    ../clib/fenv.c                           0x00000000   Number         0  _rserrno.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/locale.s                         0x00000000   Number         0  lc_numeric_c.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  lludiv10.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memcpy_w.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  strcmpv7m.o ABSOLUTE
    ../clib/misc.s                           0x00000000   Number         0  printf_stubs.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_common.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_intcommon.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_infnan.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_nopercent.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __2printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __2sprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  noretval__2printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  noretval__2sprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_pad.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_str.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_file.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _sputc.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_d.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_x.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_s.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_f.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent_end.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  ferror_locked.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  ferror.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strlen.o ABSOLUTE
    ../fplib/basic.s                         0x00000000   Number         0  basic.o ABSOLUTE
    ../fplib/d2f.s                           0x00000000   Number         0  d2f.o ABSOLUTE
    ../fplib/daddsub.s                       0x00000000   Number         0  daddsub_clz.o ABSOLUTE
    ../fplib/dcmpi.s                         0x00000000   Number         0  dcmpi.o ABSOLUTE
    ../fplib/ddiv.s                          0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/deqf.s                          0x00000000   Number         0  deqf.o ABSOLUTE
    ../fplib/dfix.s                          0x00000000   Number         0  dfix.o ABSOLUTE
    ../fplib/dfixu.s                         0x00000000   Number         0  dfixu.o ABSOLUTE
    ../fplib/dflt.s                          0x00000000   Number         0  dflt_clz.o ABSOLUTE
    ../fplib/dleqf.s                         0x00000000   Number         0  dleqf.o ABSOLUTE
    ../fplib/dmul.s                          0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/dnaninf.s                       0x00000000   Number         0  dnaninf.o ABSOLUTE
    ../fplib/dretinf.s                       0x00000000   Number         0  dretinf.o ABSOLUTE
    ../fplib/dsqrt.s                         0x00000000   Number         0  dsqrt_umaal.o ABSOLUTE
    ../fplib/f2d.s                           0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/fnaninf.s                       0x00000000   Number         0  fnaninf.o ABSOLUTE
    ../fplib/fpinit.s                        0x00000000   Number         0  fpinit.o ABSOLUTE
    ../fplib/fretinf.s                       0x00000000   Number         0  fretinf.o ABSOLUTE
    ../fplib/istatus.s                       0x00000000   Number         0  istatus.o ABSOLUTE
    ../fplib/printf1.s                       0x00000000   Number         0  printf1.o ABSOLUTE
    ../fplib/usenofp.s                       0x00000000   Number         0  usenofp.o ABSOLUTE
    ../mathlib/atan.c                        0x00000000   Number         0  atan.o ABSOLUTE
    ../mathlib/atan.c                        0x00000000   Number         0  atan_x.o ABSOLUTE
    ../mathlib/atan2.c                       0x00000000   Number         0  atan2.o ABSOLUTE
    ../mathlib/atan2.c                       0x00000000   Number         0  atan2_x.o ABSOLUTE
    ../mathlib/atan2f.c                      0x00000000   Number         0  atan2f.o ABSOLUTE
    ../mathlib/atan2f.c                      0x00000000   Number         0  atan2f_x.o ABSOLUTE
    ../mathlib/cos.c                         0x00000000   Number         0  cos.o ABSOLUTE
    ../mathlib/cos.c                         0x00000000   Number         0  cos_x.o ABSOLUTE
    ../mathlib/cos_i.c                       0x00000000   Number         0  cos_i.o ABSOLUTE
    ../mathlib/dunder.c                      0x00000000   Number         0  dunder.o ABSOLUTE
    ../mathlib/fabs.c                        0x00000000   Number         0  fabs.o ABSOLUTE
    ../mathlib/fpclassify.c                  0x00000000   Number         0  fpclassify.o ABSOLUTE
    ../mathlib/fpclassifyf.c                 0x00000000   Number         0  fpclassifyf.o ABSOLUTE
    ../mathlib/funder.c                      0x00000000   Number         0  funder.o ABSOLUTE
    ../mathlib/poly.c                        0x00000000   Number         0  poly.o ABSOLUTE
    ../mathlib/qnan.c                        0x00000000   Number         0  qnan.o ABSOLUTE
    ../mathlib/rred.c                        0x00000000   Number         0  rred.o ABSOLUTE
    ../mathlib/rredf.c                       0x00000000   Number         0  rredf.o ABSOLUTE
    ../mathlib/sin.c                         0x00000000   Number         0  sin.o ABSOLUTE
    ../mathlib/sin.c                         0x00000000   Number         0  sin_x.o ABSOLUTE
    ../mathlib/sin_i.c                       0x00000000   Number         0  sin_i.o ABSOLUTE
    ../mathlib/sin_i.c                       0x00000000   Number         0  sin_i_x.o ABSOLUTE
    ../mathlib/sinf.c                        0x00000000   Number         0  sinf_x.o ABSOLUTE
    ../mathlib/sinf.c                        0x00000000   Number         0  sinf.o ABSOLUTE
    ../mathlib/sqrt.c                        0x00000000   Number         0  sqrt.o ABSOLUTE
    ../mathlib/sqrt.c                        0x00000000   Number         0  sqrt_x.o ABSOLUTE
    ../mathlib/sqrtf.c                       0x00000000   Number         0  sqrtf.o ABSOLUTE
    ../mathlib/sqrtf.c                       0x00000000   Number         0  sqrtf_x.o ABSOLUTE
    ..\CORE\startup_stm32f40_41xxx.s         0x00000000   Number         0  startup_stm32f40_41xxx.o ABSOLUTE
    ..\CommonTables\arm_common_tables.c      0x00000000   Number         0  arm_common_tables.o ABSOLUTE
    ..\ComplexMathFunctions\arm_cmplx_mag_f32.c 0x00000000   Number         0  arm_cmplx_mag_f32.o ABSOLUTE
    ..\FWLIB\src\misc.c                      0x00000000   Number         0  misc.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_adc.c             0x00000000   Number         0  stm32f4xx_adc.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_can.c             0x00000000   Number         0  stm32f4xx_can.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_crc.c             0x00000000   Number         0  stm32f4xx_crc.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_cryp.c            0x00000000   Number         0  stm32f4xx_cryp.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_cryp_aes.c        0x00000000   Number         0  stm32f4xx_cryp_aes.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_cryp_des.c        0x00000000   Number         0  stm32f4xx_cryp_des.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_cryp_tdes.c       0x00000000   Number         0  stm32f4xx_cryp_tdes.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_dac.c             0x00000000   Number         0  stm32f4xx_dac.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_dbgmcu.c          0x00000000   Number         0  stm32f4xx_dbgmcu.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_dcmi.c            0x00000000   Number         0  stm32f4xx_dcmi.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_dma.c             0x00000000   Number         0  stm32f4xx_dma.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_dma2d.c           0x00000000   Number         0  stm32f4xx_dma2d.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_exti.c            0x00000000   Number         0  stm32f4xx_exti.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_flash.c           0x00000000   Number         0  stm32f4xx_flash.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_flash_ramfunc.c   0x00000000   Number         0  stm32f4xx_flash_ramfunc.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_fsmc.c            0x00000000   Number         0  stm32f4xx_fsmc.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_gpio.c            0x00000000   Number         0  stm32f4xx_gpio.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_hash.c            0x00000000   Number         0  stm32f4xx_hash.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_hash_md5.c        0x00000000   Number         0  stm32f4xx_hash_md5.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_hash_sha1.c       0x00000000   Number         0  stm32f4xx_hash_sha1.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_i2c.c             0x00000000   Number         0  stm32f4xx_i2c.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_iwdg.c            0x00000000   Number         0  stm32f4xx_iwdg.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_ltdc.c            0x00000000   Number         0  stm32f4xx_ltdc.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_pwr.c             0x00000000   Number         0  stm32f4xx_pwr.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_rcc.c             0x00000000   Number         0  stm32f4xx_rcc.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_rng.c             0x00000000   Number         0  stm32f4xx_rng.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_rtc.c             0x00000000   Number         0  stm32f4xx_rtc.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_sai.c             0x00000000   Number         0  stm32f4xx_sai.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_sdio.c            0x00000000   Number         0  stm32f4xx_sdio.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_spi.c             0x00000000   Number         0  stm32f4xx_spi.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_syscfg.c          0x00000000   Number         0  stm32f4xx_syscfg.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_tim.c             0x00000000   Number         0  stm32f4xx_tim.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_usart.c           0x00000000   Number         0  stm32f4xx_usart.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_wwdg.c            0x00000000   Number         0  stm32f4xx_wwdg.o ABSOLUTE
    ..\HARDWARE\AD9833\AD9833.c              0x00000000   Number         0  ad9833.o ABSOLUTE
    ..\HARDWARE\AD9833\spi.c                 0x00000000   Number         0  spi.o ABSOLUTE
    ..\HARDWARE\ADC\adc.c                    0x00000000   Number         0  adc.o ABSOLUTE
    ..\HARDWARE\DAC\dac.c                    0x00000000   Number         0  dac.o ABSOLUTE
    ..\HARDWARE\FFT\fft.c                    0x00000000   Number         0  fft.o ABSOLUTE
    ..\HARDWARE\G\G.c                        0x00000000   Number         0  g.o ABSOLUTE
    ..\HARDWARE\KALMAN\kalman.c              0x00000000   Number         0  kalman.o ABSOLUTE
    ..\HARDWARE\LCD\lcd.c                    0x00000000   Number         0  lcd.o ABSOLUTE
    ..\HARDWARE\LCD\lcd_ex.c                 0x00000000   Number         0  lcd_ex.o ABSOLUTE
    ..\HARDWARE\LED\led.c                    0x00000000   Number         0  led.o ABSOLUTE
    ..\HARDWARE\TIMER2\timer.c               0x00000000   Number         0  timer.o ABSOLUTE
    ..\HARDWARE\TOUCH\touch.c                0x00000000   Number         0  touch_1.o ABSOLUTE
    ..\HARDWARE\key\stm32f4_key.c            0x00000000   Number         0  stm32f4_key.o ABSOLUTE
    ..\SYSTEM\delay\delay.c                  0x00000000   Number         0  delay.o ABSOLUTE
    ..\SYSTEM\sys\sys.c                      0x00000000   Number         0  sys.o ABSOLUTE
    ..\SYSTEM\usart\usart.c                  0x00000000   Number         0  usart.o ABSOLUTE
    ..\TransformFunctions\arm_bitreversal.c  0x00000000   Number         0  arm_bitreversal.o ABSOLUTE
    ..\TransformFunctions\arm_cfft_radix4_f32.c 0x00000000   Number         0  arm_cfft_radix4_f32.o ABSOLUTE
    ..\TransformFunctions\arm_cfft_radix4_init_f32.c 0x00000000   Number         0  arm_cfft_radix4_init_f32.o ABSOLUTE
    ..\\CommonTables\\arm_common_tables.c    0x00000000   Number         0  arm_common_tables.o ABSOLUTE
    ..\\ComplexMathFunctions\\arm_cmplx_mag_f32.c 0x00000000   Number         0  arm_cmplx_mag_f32.o ABSOLUTE
    ..\\FWLIB\\src\\misc.c                   0x00000000   Number         0  misc.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_adc.c          0x00000000   Number         0  stm32f4xx_adc.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_can.c          0x00000000   Number         0  stm32f4xx_can.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_crc.c          0x00000000   Number         0  stm32f4xx_crc.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_cryp.c         0x00000000   Number         0  stm32f4xx_cryp.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_cryp_aes.c     0x00000000   Number         0  stm32f4xx_cryp_aes.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_cryp_des.c     0x00000000   Number         0  stm32f4xx_cryp_des.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_cryp_tdes.c    0x00000000   Number         0  stm32f4xx_cryp_tdes.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_dac.c          0x00000000   Number         0  stm32f4xx_dac.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_dbgmcu.c       0x00000000   Number         0  stm32f4xx_dbgmcu.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_dcmi.c         0x00000000   Number         0  stm32f4xx_dcmi.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_dma.c          0x00000000   Number         0  stm32f4xx_dma.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_dma2d.c        0x00000000   Number         0  stm32f4xx_dma2d.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_exti.c         0x00000000   Number         0  stm32f4xx_exti.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_flash.c        0x00000000   Number         0  stm32f4xx_flash.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_flash_ramfunc.c 0x00000000   Number         0  stm32f4xx_flash_ramfunc.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_fsmc.c         0x00000000   Number         0  stm32f4xx_fsmc.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_gpio.c         0x00000000   Number         0  stm32f4xx_gpio.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_hash.c         0x00000000   Number         0  stm32f4xx_hash.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_hash_md5.c     0x00000000   Number         0  stm32f4xx_hash_md5.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_hash_sha1.c    0x00000000   Number         0  stm32f4xx_hash_sha1.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_i2c.c          0x00000000   Number         0  stm32f4xx_i2c.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_iwdg.c         0x00000000   Number         0  stm32f4xx_iwdg.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_ltdc.c         0x00000000   Number         0  stm32f4xx_ltdc.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_pwr.c          0x00000000   Number         0  stm32f4xx_pwr.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_rcc.c          0x00000000   Number         0  stm32f4xx_rcc.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_rng.c          0x00000000   Number         0  stm32f4xx_rng.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_rtc.c          0x00000000   Number         0  stm32f4xx_rtc.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_sai.c          0x00000000   Number         0  stm32f4xx_sai.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_sdio.c         0x00000000   Number         0  stm32f4xx_sdio.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_spi.c          0x00000000   Number         0  stm32f4xx_spi.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_syscfg.c       0x00000000   Number         0  stm32f4xx_syscfg.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_tim.c          0x00000000   Number         0  stm32f4xx_tim.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_usart.c        0x00000000   Number         0  stm32f4xx_usart.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_wwdg.c         0x00000000   Number         0  stm32f4xx_wwdg.o ABSOLUTE
    ..\\HARDWARE\\AD9833\\AD9833.c           0x00000000   Number         0  ad9833.o ABSOLUTE
    ..\\HARDWARE\\AD9833\\spi.c              0x00000000   Number         0  spi.o ABSOLUTE
    ..\\HARDWARE\\ADC\\adc.c                 0x00000000   Number         0  adc.o ABSOLUTE
    ..\\HARDWARE\\DAC\\dac.c                 0x00000000   Number         0  dac.o ABSOLUTE
    ..\\HARDWARE\\FFT\\fft.c                 0x00000000   Number         0  fft.o ABSOLUTE
    ..\\HARDWARE\\G\\G.c                     0x00000000   Number         0  g.o ABSOLUTE
    ..\\HARDWARE\\KALMAN\\kalman.c           0x00000000   Number         0  kalman.o ABSOLUTE
    ..\\HARDWARE\\LCD\\lcd.c                 0x00000000   Number         0  lcd.o ABSOLUTE
    ..\\HARDWARE\\LCD\\lcd_ex.c              0x00000000   Number         0  lcd_ex.o ABSOLUTE
    ..\\HARDWARE\\LED\\led.c                 0x00000000   Number         0  led.o ABSOLUTE
    ..\\HARDWARE\\TIMER2\\timer.c            0x00000000   Number         0  timer.o ABSOLUTE
    ..\\HARDWARE\\TOUCH\\touch.c             0x00000000   Number         0  touch_1.o ABSOLUTE
    ..\\HARDWARE\\key\\stm32f4_key.c         0x00000000   Number         0  stm32f4_key.o ABSOLUTE
    ..\\SYSTEM\\delay\\delay.c               0x00000000   Number         0  delay.o ABSOLUTE
    ..\\SYSTEM\\sys\\sys.c                   0x00000000   Number         0  sys.o ABSOLUTE
    ..\\SYSTEM\\usart\\usart.c               0x00000000   Number         0  usart.o ABSOLUTE
    ..\\TransformFunctions\\arm_bitreversal.c 0x00000000   Number         0  arm_bitreversal.o ABSOLUTE
    ..\\TransformFunctions\\arm_cfft_radix4_f32.c 0x00000000   Number         0  arm_cfft_radix4_f32.o ABSOLUTE
    ..\\TransformFunctions\\arm_cfft_radix4_init_f32.c 0x00000000   Number         0  arm_cfft_radix4_init_f32.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    main.c                                   0x00000000   Number         0  main.o ABSOLUTE
    main.c                                   0x00000000   Number         0  main.o ABSOLUTE
    stm32f4xx_it.c                           0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    stm32f4xx_it.c                           0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    system_stm32f4xx.c                       0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    system_stm32f4xx.c                       0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    RESET                                    0x08000000   Section      392  startup_stm32f40_41xxx.o(RESET)
    !!!main                                  0x08000188   Section        8  __main.o(!!!main)
    !!!scatter                               0x08000190   Section       52  __scatter.o(!!!scatter)
    !!dczerorl2                              0x080001c4   Section       90  __dczerorl2.o(!!dczerorl2)
    !!handler_zi                             0x08000220   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$_printf_percent$$00000000  0x0800023c   Section        0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    .ARM.Collect$$_printf_percent$$00000003  0x0800023c   Section        6  _printf_f.o(.ARM.Collect$$_printf_percent$$00000003)
    .ARM.Collect$$_printf_percent$$00000009  0x08000242   Section        6  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    .ARM.Collect$$_printf_percent$$0000000C  0x08000248   Section        6  _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C)
    .ARM.Collect$$_printf_percent$$00000014  0x0800024e   Section        6  _printf_s.o(.ARM.Collect$$_printf_percent$$00000014)
    .ARM.Collect$$_printf_percent$$00000017  0x08000254   Section        4  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    .ARM.Collect$$libinit$$00000000          0x08000258   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000001          0x0800025a   Section        4  libinit2.o(.ARM.Collect$$libinit$$00000001)
    .ARM.Collect$$libinit$$00000004          0x0800025e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$0000000A          0x0800025e   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    .ARM.Collect$$libinit$$0000000C          0x0800025e   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x0800025e   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$0000000F          0x0800025e   Section        6  libinit2.o(.ARM.Collect$$libinit$$0000000F)
    .ARM.Collect$$libinit$$00000011          0x08000264   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    .ARM.Collect$$libinit$$00000013          0x08000264   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000015          0x08000264   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000016          0x08000264   Section       10  libinit2.o(.ARM.Collect$$libinit$$00000016)
    .ARM.Collect$$libinit$$00000017          0x0800026e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000019          0x0800026e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x0800026e   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x0800026e   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x0800026e   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x0800026e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x0800026e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x0800026e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$0000002C          0x0800026e   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    .ARM.Collect$$libinit$$0000002E          0x0800026e   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x0800026e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x0800026e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000033          0x0800026e   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000033)
    .ARM.Collect$$libshutdown$$00000000      0x08000270   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000002      0x08000272   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    .ARM.Collect$$libshutdown$$00000004      0x08000272   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    .ARM.Collect$$libshutdown$$00000006      0x08000272   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    .ARM.Collect$$libshutdown$$00000009      0x08000272   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    .ARM.Collect$$libshutdown$$0000000C      0x08000272   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    .ARM.Collect$$libshutdown$$0000000E      0x08000272   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    .ARM.Collect$$libshutdown$$00000011      0x08000272   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000011)
    .ARM.Collect$$libshutdown$$00000012      0x08000272   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$00000012)
    .ARM.Collect$$rtentry$$00000000          0x08000274   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x08000274   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x08000274   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x0800027a   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x0800027a   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x0800027e   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x0800027e   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x08000286   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x08000288   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x08000288   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x0800028c   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    .text                                    0x08000294   Section        0  main.o(.text)
    .text                                    0x08002528   Section        0  stm32f4xx_it.o(.text)
    .text                                    0x08002544   Section        0  system_stm32f4xx.o(.text)
    SetSysClock                              0x08002545   Thumb Code   220  system_stm32f4xx.o(.text)
    .text                                    0x08002754   Section       64  startup_stm32f40_41xxx.o(.text)
    $v0                                      0x08002754   Number         0  startup_stm32f40_41xxx.o(.text)
    .text                                    0x08002794   Section        0  misc.o(.text)
    .text                                    0x08002874   Section        0  stm32f4xx_adc.o(.text)
    .text                                    0x08002cd8   Section        0  stm32f4xx_dac.o(.text)
    .text                                    0x08002ee8   Section        0  stm32f4xx_dma.o(.text)
    .text                                    0x08003290   Section        0  stm32f4xx_gpio.o(.text)
    .text                                    0x08003524   Section        0  stm32f4xx_rcc.o(.text)
    .text                                    0x08003b80   Section        0  stm32f4xx_tim.o(.text)
    TI4_Config                               0x0800431f   Thumb Code    80  stm32f4xx_tim.o(.text)
    TI3_Config                               0x08004381   Thumb Code    72  stm32f4xx_tim.o(.text)
    TI2_Config                               0x080043e3   Thumb Code    90  stm32f4xx_tim.o(.text)
    TI1_Config                               0x0800444f   Thumb Code    58  stm32f4xx_tim.o(.text)
    .text                                    0x08004824   Section        0  stm32f4xx_usart.o(.text)
    .text                                    0x08004c78   Section        0  delay.o(.text)
    .text                                    0x08004d7c   Section        0  sys.o(.text)
    sys_nvic_priority_group_config           0x08004d89   Thumb Code    36  sys.o(.text)
    .text                                    0x080052f4   Section        0  usart.o(.text)
    .text                                    0x08005450   Section        0  stm32f4_key.o(.text)
    .text                                    0x0800565c   Section        0  led.o(.text)
    .text                                    0x0800569c   Section        0  timer.o(.text)
    .text                                    0x0800580c   Section        0  kalman.o(.text)
    .text                                    0x08005990   Section        0  adc.o(.text)
    .text                                    0x080060ec   Section        0  fft.o(.text)
    .text                                    0x08006838   Section        0  lcd.o(.text)
    lcd_opt_delay                            0x0800686f   Thumb Code    12  lcd.o(.text)
    lcd_rd_data                              0x0800687b   Thumb Code    20  lcd.o(.text)
    lcd_pow                                  0x08007a87   Thumb Code    22  lcd.o(.text)
    .text                                    0x08007c7c   Section        0  lcd_ex.o(.text)
    .text                                    0x0800a524   Section        0  ad9833.o(.text)
    .text                                    0x0800a880   Section        0  dac.o(.text)
    DAC_GenerateSineTable                    0x0800a941   Thumb Code   148  dac.o(.text)
    .text                                    0x0800ad58   Section        0  arm_cmplx_mag_f32.o(.text)
    .text                                    0x0800ae48   Section        0  arm_cfft_radix4_f32.o(.text)
    .text                                    0x0800b4e8   Section        0  arm_cfft_radix4_init_f32.o(.text)
    .text                                    0x0800b5a8   Section        0  arm_bitreversal.o(.text)
    .text                                    0x0800b78e   Section        2  use_no_semi_2.o(.text)
    .text                                    0x0800b790   Section        0  noretval__2printf.o(.text)
    .text                                    0x0800b7a8   Section        0  noretval__2sprintf.o(.text)
    .text                                    0x0800b7d0   Section        0  _printf_pad.o(.text)
    .text                                    0x0800b81e   Section        0  _printf_str.o(.text)
    .text                                    0x0800b870   Section        0  _printf_dec.o(.text)
    .text                                    0x0800b8e8   Section        0  _printf_hex_int.o(.text)
    .text                                    0x0800b940   Section        0  __printf_flags_wp.o(.text)
    .text                                    0x0800ba78   Section        0  strlen.o(.text)
    .text                                    0x0800bab6   Section      100  rt_memcpy_w.o(.text)
    .text                                    0x0800bb1a   Section        0  heapauxi.o(.text)
    .text                                    0x0800bb20   Section        2  use_no_semi.o(.text)
    .text                                    0x0800bb22   Section        0  _rserrno.o(.text)
    .text                                    0x0800bb38   Section        0  _printf_intcommon.o(.text)
    .text                                    0x0800bbea   Section        0  _printf_fp_dec.o(.text)
    _fp_digits                               0x0800bbed   Thumb Code   432  _printf_fp_dec.o(.text)
    .text                                    0x0800c008   Section        0  _printf_char_common.o(.text)
    _printf_input_char                       0x0800c009   Thumb Code    10  _printf_char_common.o(.text)
    .text                                    0x0800c038   Section        0  _sputc.o(.text)
    .text                                    0x0800c042   Section        0  _printf_char.o(.text)
    .text                                    0x0800c070   Section        0  _printf_char_file.o(.text)
    .text                                    0x0800c094   Section        8  rt_locale_intlibspace.o(.text)
    .text                                    0x0800c09c   Section        8  rt_errno_addr_intlibspace.o(.text)
    .text                                    0x0800c0a4   Section      138  lludiv10.o(.text)
    .text                                    0x0800c130   Section        0  _printf_fp_infnan.o(.text)
    .text                                    0x0800c1b0   Section        0  bigflt0.o(.text)
    .text                                    0x0800c294   Section        0  ferror.o(.text)
    .text                                    0x0800c29c   Section        8  libspace.o(.text)
    .text                                    0x0800c2a4   Section       74  sys_stackheap_outer.o(.text)
    .text                                    0x0800c2ee   Section        0  exit.o(.text)
    .text                                    0x0800c300   Section      128  strcmpv7m.o(.text)
    CL$$btod_d2e                             0x0800c380   Section       62  btod.o(CL$$btod_d2e)
    CL$$btod_d2e_denorm_low                  0x0800c3be   Section       70  btod.o(CL$$btod_d2e_denorm_low)
    CL$$btod_d2e_norm_op1                    0x0800c404   Section       96  btod.o(CL$$btod_d2e_norm_op1)
    CL$$btod_div_common                      0x0800c464   Section      824  btod.o(CL$$btod_div_common)
    CL$$btod_e2e                             0x0800c79c   Section      220  btod.o(CL$$btod_e2e)
    CL$$btod_ediv                            0x0800c878   Section       42  btod.o(CL$$btod_ediv)
    CL$$btod_emul                            0x0800c8a2   Section       42  btod.o(CL$$btod_emul)
    CL$$btod_mult_common                     0x0800c8cc   Section      580  btod.o(CL$$btod_mult_common)
    i.__ARM_fpclassify                       0x0800cb10   Section        0  fpclassify.o(i.__ARM_fpclassify)
    i.__ARM_fpclassifyf                      0x0800cb40   Section        0  fpclassifyf.o(i.__ARM_fpclassifyf)
    i.__hardfp_atan                          0x0800cb68   Section        0  atan.o(i.__hardfp_atan)
    i.__hardfp_atan2                         0x0800ce40   Section        0  atan2.o(i.__hardfp_atan2)
    i.__hardfp_atan2f                        0x0800d040   Section        0  atan2f.o(i.__hardfp_atan2f)
    i.__hardfp_cos                           0x0800d2f0   Section        0  cos.o(i.__hardfp_cos)
    i.__hardfp_sin                           0x0800d3b8   Section        0  sin.o(i.__hardfp_sin)
    i.__hardfp_sinf                          0x0800d480   Section        0  sinf.o(i.__hardfp_sinf)
    i.__hardfp_sqrt                          0x0800d610   Section        0  sqrt.o(i.__hardfp_sqrt)
    i.__hardfp_sqrtf                         0x0800d68a   Section        0  sqrtf.o(i.__hardfp_sqrtf)
    i.__ieee754_rem_pio2                     0x0800d6c8   Section        0  rred.o(i.__ieee754_rem_pio2)
    i.__kernel_cos                           0x0800db00   Section        0  cos_i.o(i.__kernel_cos)
    i.__kernel_poly                          0x0800dc70   Section        0  poly.o(i.__kernel_poly)
    i.__kernel_sin                           0x0800dd68   Section        0  sin_i.o(i.__kernel_sin)
    i.__mathlib_dbl_infnan                   0x0800de98   Section        0  dunder.o(i.__mathlib_dbl_infnan)
    i.__mathlib_dbl_infnan2                  0x0800deac   Section        0  dunder.o(i.__mathlib_dbl_infnan2)
    i.__mathlib_dbl_invalid                  0x0800dec0   Section        0  dunder.o(i.__mathlib_dbl_invalid)
    i.__mathlib_dbl_underflow                0x0800dee0   Section        0  dunder.o(i.__mathlib_dbl_underflow)
    i.__mathlib_flt_infnan                   0x0800df00   Section        0  funder.o(i.__mathlib_flt_infnan)
    i.__mathlib_flt_infnan2                  0x0800df06   Section        0  funder.o(i.__mathlib_flt_infnan2)
    i.__mathlib_flt_invalid                  0x0800df0c   Section        0  funder.o(i.__mathlib_flt_invalid)
    i.__mathlib_flt_underflow                0x0800df1c   Section        0  funder.o(i.__mathlib_flt_underflow)
    i.__mathlib_rredf2                       0x0800df2c   Section        0  rredf.o(i.__mathlib_rredf2)
    i._is_digit                              0x0800e080   Section        0  __printf_wp.o(i._is_digit)
    i.atan                                   0x0800e08e   Section        0  atan.o(i.atan)
    i.fabs                                   0x0800e09e   Section        0  fabs.o(i.fabs)
    locale$$code                             0x0800e0b8   Section       44  lc_numeric_c.o(locale$$code)
    x$fpl$basic                              0x0800e0e4   Section       24  basic.o(x$fpl$basic)
    $v0                                      0x0800e0e4   Number         0  basic.o(x$fpl$basic)
    x$fpl$d2f                                0x0800e0fc   Section       98  d2f.o(x$fpl$d2f)
    $v0                                      0x0800e0fc   Number         0  d2f.o(x$fpl$d2f)
    x$fpl$dadd                               0x0800e160   Section      336  daddsub_clz.o(x$fpl$dadd)
    $v0                                      0x0800e160   Number         0  daddsub_clz.o(x$fpl$dadd)
    _dadd1                                   0x0800e171   Thumb Code     0  daddsub_clz.o(x$fpl$dadd)
    x$fpl$dcmpinf                            0x0800e2b0   Section       24  dcmpi.o(x$fpl$dcmpinf)
    $v0                                      0x0800e2b0   Number         0  dcmpi.o(x$fpl$dcmpinf)
    x$fpl$ddiv                               0x0800e2c8   Section      688  ddiv.o(x$fpl$ddiv)
    $v0                                      0x0800e2c8   Number         0  ddiv.o(x$fpl$ddiv)
    ddiv_entry                               0x0800e2cf   Thumb Code     0  ddiv.o(x$fpl$ddiv)
    x$fpl$deqf                               0x0800e578   Section      120  deqf.o(x$fpl$deqf)
    $v0                                      0x0800e578   Number         0  deqf.o(x$fpl$deqf)
    x$fpl$dfix                               0x0800e5f0   Section       94  dfix.o(x$fpl$dfix)
    $v0                                      0x0800e5f0   Number         0  dfix.o(x$fpl$dfix)
    x$fpl$dfixu                              0x0800e650   Section       90  dfixu.o(x$fpl$dfixu)
    $v0                                      0x0800e650   Number         0  dfixu.o(x$fpl$dfixu)
    x$fpl$dflt                               0x0800e6aa   Section       46  dflt_clz.o(x$fpl$dflt)
    $v0                                      0x0800e6aa   Number         0  dflt_clz.o(x$fpl$dflt)
    x$fpl$dfltu                              0x0800e6d8   Section       38  dflt_clz.o(x$fpl$dfltu)
    $v0                                      0x0800e6d8   Number         0  dflt_clz.o(x$fpl$dfltu)
    x$fpl$dmul                               0x0800e700   Section      340  dmul.o(x$fpl$dmul)
    $v0                                      0x0800e700   Number         0  dmul.o(x$fpl$dmul)
    x$fpl$dnaninf                            0x0800e854   Section      156  dnaninf.o(x$fpl$dnaninf)
    $v0                                      0x0800e854   Number         0  dnaninf.o(x$fpl$dnaninf)
    x$fpl$dretinf                            0x0800e8f0   Section       12  dretinf.o(x$fpl$dretinf)
    $v0                                      0x0800e8f0   Number         0  dretinf.o(x$fpl$dretinf)
    x$fpl$drsb                               0x0800e8fc   Section       22  daddsub_clz.o(x$fpl$drsb)
    $v0                                      0x0800e8fc   Number         0  daddsub_clz.o(x$fpl$drsb)
    x$fpl$dsqrt                              0x0800e914   Section      408  dsqrt_umaal.o(x$fpl$dsqrt)
    $v0                                      0x0800e914   Number         0  dsqrt_umaal.o(x$fpl$dsqrt)
    x$fpl$dsub                               0x0800eaac   Section      468  daddsub_clz.o(x$fpl$dsub)
    $v0                                      0x0800eaac   Number         0  daddsub_clz.o(x$fpl$dsub)
    _dsub1                                   0x0800eabd   Thumb Code     0  daddsub_clz.o(x$fpl$dsub)
    x$fpl$f2d                                0x0800ec80   Section       86  f2d.o(x$fpl$f2d)
    $v0                                      0x0800ec80   Number         0  f2d.o(x$fpl$f2d)
    x$fpl$fnaninf                            0x0800ecd6   Section      140  fnaninf.o(x$fpl$fnaninf)
    $v0                                      0x0800ecd6   Number         0  fnaninf.o(x$fpl$fnaninf)
    x$fpl$fpinit                             0x0800ed62   Section       10  fpinit.o(x$fpl$fpinit)
    $v0                                      0x0800ed62   Number         0  fpinit.o(x$fpl$fpinit)
    x$fpl$fretinf                            0x0800ed6c   Section       10  fretinf.o(x$fpl$fretinf)
    $v0                                      0x0800ed6c   Number         0  fretinf.o(x$fpl$fretinf)
    x$fpl$printf1                            0x0800ed76   Section        4  printf1.o(x$fpl$printf1)
    $v0                                      0x0800ed76   Number         0  printf1.o(x$fpl$printf1)
    .constdata                               0x0800ed7a   Section    12160  lcd.o(.constdata)
    x$fpl$usenofp                            0x0800ed7a   Section        0  usenofp.o(x$fpl$usenofp)
    .constdata                               0x08011cfc   Section        5  ad9833.o(.constdata)
    .constdata                               0x08011d04   Section      120  dac.o(.constdata)
    amplitude_table                          0x08011d04   Data         120  dac.o(.constdata)
    .constdata                               0x08011d7c   Section     2048  arm_common_tables.o(.constdata)
    .constdata                               0x0801257c   Section    32768  arm_common_tables.o(.constdata)
    .constdata                               0x0801a57c   Section       40  _printf_hex_int.o(.constdata)
    uc_hextab                                0x0801a57c   Data          20  _printf_hex_int.o(.constdata)
    lc_hextab                                0x0801a590   Data          20  _printf_hex_int.o(.constdata)
    .constdata                               0x0801a5a4   Section       17  __printf_flags_wp.o(.constdata)
    maptable                                 0x0801a5a4   Data          17  __printf_flags_wp.o(.constdata)
    .constdata                               0x0801a5b8   Section      152  atan.o(.constdata)
    atanhi                                   0x0801a5b8   Data          32  atan.o(.constdata)
    atanlo                                   0x0801a5d8   Data          32  atan.o(.constdata)
    aTodd                                    0x0801a5f8   Data          40  atan.o(.constdata)
    aTeven                                   0x0801a620   Data          48  atan.o(.constdata)
    .constdata                               0x0801a650   Section       48  cos_i.o(.constdata)
    C                                        0x0801a650   Data          48  cos_i.o(.constdata)
    .constdata                               0x0801a680   Section        8  qnan.o(.constdata)
    .constdata                               0x0801a688   Section      200  rred.o(.constdata)
    pio2s                                    0x0801a688   Data          48  rred.o(.constdata)
    twooverpi                                0x0801a6b8   Data         152  rred.o(.constdata)
    .constdata                               0x0801a750   Section       32  rredf.o(.constdata)
    twooverpi                                0x0801a750   Data          32  rredf.o(.constdata)
    .constdata                               0x0801a770   Section       40  sin_i.o(.constdata)
    S                                        0x0801a770   Data          40  sin_i.o(.constdata)
    .constdata                               0x0801a798   Section      148  bigflt0.o(.constdata)
    tenpwrs_x                                0x0801a798   Data          60  bigflt0.o(.constdata)
    tenpwrs_i                                0x0801a7d4   Data          64  bigflt0.o(.constdata)
    .conststring                             0x0801a82c   Section       58  main.o(.conststring)
    locale$$data                             0x0801a888   Section       28  lc_numeric_c.o(locale$$data)
    __lcnum_c_name                           0x0801a88c   Data           2  lc_numeric_c.o(locale$$data)
    __lcnum_c_start                          0x0801a894   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_point                          0x0801a8a0   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_thousands                      0x0801a8a2   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_grouping                       0x0801a8a3   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_end                            0x0801a8a4   Data           0  lc_numeric_c.o(locale$$data)
    .data                                    0x20000000   Section      332  main.o(.data)
    status_counter                           0x20000148   Data           4  main.o(.data)
    .data                                    0x2000014c   Section       20  system_stm32f4xx.o(.data)
    .data                                    0x20000160   Section       16  stm32f4xx_rcc.o(.data)
    APBAHBPrescTable                         0x20000160   Data          16  stm32f4xx_rcc.o(.data)
    .data                                    0x20000170   Section        4  delay.o(.data)
    fac_us                                   0x20000170   Data           1  delay.o(.data)
    fac_ms                                   0x20000172   Data           2  delay.o(.data)
    .data                                    0x20000174   Section       10  usart.o(.data)
    .data                                    0x20000180   Section        4  timer.o(.data)
    .data                                    0x20000184   Section        4  kalman.o(.data)
    .data                                    0x20000188   Section       39  adc.o(.data)
    .data                                    0x200001b0   Section       60  fft.o(.data)
    i                                        0x200001ea   Data           2  fft.o(.data)
    .data                                    0x200001ec   Section        8  lcd.o(.data)
    .data                                    0x200001f4   Section       28  dac.o(.data)
    sine_index                               0x20000200   Data           4  dac.o(.data)
    phase_increment                          0x20000204   Data           4  dac.o(.data)
    current_phase                            0x20000208   Data           4  dac.o(.data)
    current_amplitude                        0x2000020c   Data           4  dac.o(.data)
    .bss                                     0x20000210   Section    32820  main.o(.bss)
    filtered_buffer                          0x20004244   Data       16384  main.o(.bss)
    .bss                                     0x20008244   Section      200  usart.o(.bss)
    .bss                                     0x2000830c   Section      308  kalman.o(.bss)
    .bss                                     0x20008440   Section    24576  adc.o(.bss)
    .bss                                     0x2000e440   Section    65680  fft.o(.bss)
    .bss                                     0x2001e4d0   Section       14  lcd.o(.bss)
    .bss                                     0x2001e4de   Section      512  dac.o(.bss)
    sine_table                               0x2001e4de   Data         512  dac.o(.bss)
    .bss                                     0x2001e6e0   Section       96  libspace.o(.bss)
    HEAP                                     0x2001e740   Section      512  startup_stm32f40_41xxx.o(HEAP)
    Heap_Mem                                 0x2001e740   Data         512  startup_stm32f40_41xxx.o(HEAP)
    STACK                                    0x2001e940   Section     1024  startup_stm32f40_41xxx.o(STACK)
    Stack_Mem                                0x2001e940   Data        1024  startup_stm32f40_41xxx.o(STACK)
    __initial_sp                             0x2001ed40   Data           0  startup_stm32f40_41xxx.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPi3$EXTD16$VFPS$VFMA$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEX$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  printf_stubs.o ABSOLUTE
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_fini_                               - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_ctype                             - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _init_alloc                               - Undefined Weak Reference
    _init_user_alloc                          - Undefined Weak Reference
    _initio                                   - Undefined Weak Reference
    _mutex_acquire                            - Undefined Weak Reference
    _mutex_release                            - Undefined Weak Reference
    _printf_mbtowc                            - Undefined Weak Reference
    _printf_truncate_signed                   - Undefined Weak Reference
    _printf_truncate_unsigned                 - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    _terminate_alloc                          - Undefined Weak Reference
    _terminate_user_alloc                     - Undefined Weak Reference
    _terminateio                              - Undefined Weak Reference
    __Vectors_Size                           0x00000188   Number         0  startup_stm32f40_41xxx.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f40_41xxx.o(RESET)
    __Vectors_End                            0x08000188   Data           0  startup_stm32f40_41xxx.o(RESET)
    __main                                   0x08000189   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x08000191   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x08000191   Thumb Code    44  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x08000191   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_null                       0x0800019f   Thumb Code     0  __scatter.o(!!!scatter)
    __decompress                             0x080001c5   Thumb Code    90  __dczerorl2.o(!!dczerorl2)
    __decompress1                            0x080001c5   Thumb Code     0  __dczerorl2.o(!!dczerorl2)
    __scatterload_zeroinit                   0x08000221   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    _printf_f                                0x0800023d   Thumb Code     0  _printf_f.o(.ARM.Collect$$_printf_percent$$00000003)
    _printf_percent                          0x0800023d   Thumb Code     0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    _printf_d                                0x08000243   Thumb Code     0  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    _printf_x                                0x08000249   Thumb Code     0  _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C)
    _printf_s                                0x0800024f   Thumb Code     0  _printf_s.o(.ARM.Collect$$_printf_percent$$00000014)
    _printf_percent_end                      0x08000255   Thumb Code     0  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    __rt_lib_init                            0x08000259   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_fp_1                       0x0800025b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000001)
    __rt_lib_init_heap_1                     0x0800025f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    __rt_lib_init_lc_common                  0x0800025f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000F)
    __rt_lib_init_preinit_1                  0x0800025f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_rand_1                     0x0800025f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_init_user_alloc_1               0x0800025f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_init_lc_collate_1               0x08000265   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    __rt_lib_init_lc_ctype_1                 0x08000265   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_monetary_1              0x08000265   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_numeric_2               0x08000265   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000016)
    __rt_lib_init_alloca_1                   0x0800026f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_argv_1                     0x0800026f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    __rt_lib_init_atexit_1                   0x0800026f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_clock_1                    0x0800026f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_cpp_1                      0x0800026f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_exceptions_1               0x0800026f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_fp_trap_1                  0x0800026f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_getenv_1                   0x0800026f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_lc_numeric_1               0x0800026f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_time_1                  0x0800026f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_return                     0x0800026f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000033)
    __rt_lib_init_signal_1                   0x0800026f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_stdio_1                    0x0800026f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_shutdown                        0x08000271   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_cpp_1                  0x08000273   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    __rt_lib_shutdown_fini_1                 0x08000273   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    __rt_lib_shutdown_fp_trap_1              0x08000273   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    __rt_lib_shutdown_heap_1                 0x08000273   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000011)
    __rt_lib_shutdown_return                 0x08000273   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000012)
    __rt_lib_shutdown_signal_1               0x08000273   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    __rt_lib_shutdown_stdio_1                0x08000273   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    __rt_lib_shutdown_user_alloc_1           0x08000273   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    __rt_entry                               0x08000275   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x08000275   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x08000275   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x0800027b   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x0800027b   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x0800027f   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x0800027f   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x08000287   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x08000289   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x08000289   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x0800028d   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    format_frequency_display                 0x08000295   Thumb Code   142  main.o(.text)
    draw_button                              0x08000323   Thumb Code   306  main.o(.text)
    draw_all_buttons                         0x08000455   Thumb Code    44  main.o(.text)
    check_button_press                       0x08000481   Thumb Code   104  main.o(.text)
    adjust_frequency                         0x080004e9   Thumb Code   122  main.o(.text)
    Print_ADC_Status                         0x08000563   Thumb Code    36  main.o(.text)
    ADC2_StartSampling                       0x08000587   Thumb Code    50  main.o(.text)
    ADC1_StartSampling                       0x080005b9   Thumb Code    50  main.o(.text)
    ADC2_StopSampling                        0x080005eb   Thumb Code    42  main.o(.text)
    ADC1_StopSampling                        0x08000615   Thumb Code    12  main.o(.text)
    AmplitudeAnalysis_Stop                   0x08000621   Thumb Code     8  main.o(.text)
    Sweep_Stop                               0x08000629   Thumb Code   342  main.o(.text)
    Convert_RMS_Amplitude_to_Voltage         0x0800077f   Thumb Code    38  main.o(.text)
    Calculate_RMS_Amplitude                  0x080007a5   Thumb Code    96  main.o(.text)
    Apply_HighPass_Filter                    0x08000805   Thumb Code   112  main.o(.text)
    Calculate_Signal_Amplitude_RMS           0x08000875   Thumb Code    50  main.o(.text)
    Process_Amplitude_Data                   0x080008a7   Thumb Code   382  main.o(.text)
    AmplitudeAnalysis_Start                  0x08000a25   Thumb Code    16  main.o(.text)
    ADC_Diagnostics                          0x08000a35   Thumb Code   958  main.o(.text)
    Sweep_Start                              0x08000df3   Thumb Code   172  main.o(.text)
    ADC_Simple_Test                          0x08000e9f   Thumb Code   246  main.o(.text)
    ADC_Manual_Test                          0x08000f95   Thumb Code   160  main.o(.text)
    ADC_Basic_Test                           0x08001035   Thumb Code   106  main.o(.text)
    AmplitudeAnalysis_Init                   0x0800109f   Thumb Code   990  main.o(.text)
    Sweep_Init                               0x0800147d   Thumb Code    78  main.o(.text)
    TIM5_Sweep_Init                          0x080014cb   Thumb Code    86  main.o(.text)
    main                                     0x08001521   Thumb Code  2940  main.o(.text)
    ADC1_ResetSampling                       0x0800209d   Thumb Code    32  main.o(.text)
    ADC2_IsSamplingComplete                  0x080020bd   Thumb Code     6  main.o(.text)
    Sweep_Update                             0x080020c3   Thumb Code    18  main.o(.text)
    Get_FFT_Bin_Index                        0x080020d5   Thumb Code    60  main.o(.text)
    Calculate_Signal_Amplitude_Phase_FFT     0x08002111   Thumb Code   362  main.o(.text)
    Convert_FFT_Amplitude_to_Voltage         0x0800227b   Thumb Code    40  main.o(.text)
    Process_Amplitude_Data_FFT               0x080022a3   Thumb Code   602  main.o(.text)
    NMI_Handler                              0x08002529   Thumb Code     2  stm32f4xx_it.o(.text)
    HardFault_Handler                        0x0800252b   Thumb Code     4  stm32f4xx_it.o(.text)
    MemManage_Handler                        0x0800252f   Thumb Code     4  stm32f4xx_it.o(.text)
    BusFault_Handler                         0x08002533   Thumb Code     4  stm32f4xx_it.o(.text)
    UsageFault_Handler                       0x08002537   Thumb Code     4  stm32f4xx_it.o(.text)
    SVC_Handler                              0x0800253b   Thumb Code     2  stm32f4xx_it.o(.text)
    DebugMon_Handler                         0x0800253d   Thumb Code     2  stm32f4xx_it.o(.text)
    PendSV_Handler                           0x0800253f   Thumb Code     2  stm32f4xx_it.o(.text)
    SysTick_Handler                          0x08002541   Thumb Code     2  stm32f4xx_it.o(.text)
    SystemInit                               0x08002621   Thumb Code    88  system_stm32f4xx.o(.text)
    SystemCoreClockUpdate                    0x08002679   Thumb Code   174  system_stm32f4xx.o(.text)
    Reset_Handler                            0x08002755   Thumb Code     8  startup_stm32f40_41xxx.o(.text)
    CAN1_RX0_IRQHandler                      0x0800276f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN1_RX1_IRQHandler                      0x0800276f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN1_SCE_IRQHandler                      0x0800276f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN1_TX_IRQHandler                       0x0800276f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN2_RX0_IRQHandler                      0x0800276f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN2_RX1_IRQHandler                      0x0800276f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN2_SCE_IRQHandler                      0x0800276f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN2_TX_IRQHandler                       0x0800276f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CRYP_IRQHandler                          0x0800276f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DCMI_IRQHandler                          0x0800276f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream0_IRQHandler                  0x0800276f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream1_IRQHandler                  0x0800276f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream2_IRQHandler                  0x0800276f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream3_IRQHandler                  0x0800276f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream4_IRQHandler                  0x0800276f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream5_IRQHandler                  0x0800276f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream6_IRQHandler                  0x0800276f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream7_IRQHandler                  0x0800276f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream3_IRQHandler                  0x0800276f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream4_IRQHandler                  0x0800276f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream5_IRQHandler                  0x0800276f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream6_IRQHandler                  0x0800276f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream7_IRQHandler                  0x0800276f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    ETH_IRQHandler                           0x0800276f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    ETH_WKUP_IRQHandler                      0x0800276f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI0_IRQHandler                         0x0800276f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI15_10_IRQHandler                     0x0800276f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI1_IRQHandler                         0x0800276f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI2_IRQHandler                         0x0800276f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI3_IRQHandler                         0x0800276f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI4_IRQHandler                         0x0800276f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI9_5_IRQHandler                       0x0800276f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    FLASH_IRQHandler                         0x0800276f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    FPU_IRQHandler                           0x0800276f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    FSMC_IRQHandler                          0x0800276f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    HASH_RNG_IRQHandler                      0x0800276f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C1_ER_IRQHandler                       0x0800276f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C1_EV_IRQHandler                       0x0800276f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C2_ER_IRQHandler                       0x0800276f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C2_EV_IRQHandler                       0x0800276f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C3_ER_IRQHandler                       0x0800276f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C3_EV_IRQHandler                       0x0800276f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_FS_IRQHandler                        0x0800276f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_FS_WKUP_IRQHandler                   0x0800276f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_HS_EP1_IN_IRQHandler                 0x0800276f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_HS_EP1_OUT_IRQHandler                0x0800276f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_HS_IRQHandler                        0x0800276f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_HS_WKUP_IRQHandler                   0x0800276f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    PVD_IRQHandler                           0x0800276f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    RCC_IRQHandler                           0x0800276f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    RTC_Alarm_IRQHandler                     0x0800276f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    RTC_WKUP_IRQHandler                      0x0800276f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    SDIO_IRQHandler                          0x0800276f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    SPI1_IRQHandler                          0x0800276f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    SPI2_IRQHandler                          0x0800276f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    SPI3_IRQHandler                          0x0800276f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TAMP_STAMP_IRQHandler                    0x0800276f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM1_BRK_TIM9_IRQHandler                 0x0800276f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM1_CC_IRQHandler                       0x0800276f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM1_TRG_COM_TIM11_IRQHandler            0x0800276f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM1_UP_TIM10_IRQHandler                 0x0800276f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM2_IRQHandler                          0x0800276f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM3_IRQHandler                          0x0800276f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM7_IRQHandler                          0x0800276f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM8_BRK_TIM12_IRQHandler                0x0800276f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM8_CC_IRQHandler                       0x0800276f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM8_TRG_COM_TIM14_IRQHandler            0x0800276f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM8_UP_TIM13_IRQHandler                 0x0800276f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    UART4_IRQHandler                         0x0800276f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    UART5_IRQHandler                         0x0800276f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    USART2_IRQHandler                        0x0800276f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    USART3_IRQHandler                        0x0800276f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    USART6_IRQHandler                        0x0800276f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    WWDG_IRQHandler                          0x0800276f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    __user_initial_stackheap                 0x08002771   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    NVIC_PriorityGroupConfig                 0x08002795   Thumb Code    10  misc.o(.text)
    NVIC_Init                                0x0800279f   Thumb Code   106  misc.o(.text)
    NVIC_SetVectorTable                      0x08002809   Thumb Code    14  misc.o(.text)
    NVIC_SystemLPConfig                      0x08002817   Thumb Code    34  misc.o(.text)
    SysTick_CLKSourceConfig                  0x08002839   Thumb Code    40  misc.o(.text)
    ADC_DeInit                               0x08002875   Thumb Code    22  stm32f4xx_adc.o(.text)
    ADC_Init                                 0x0800288b   Thumb Code    74  stm32f4xx_adc.o(.text)
    ADC_StructInit                           0x080028d5   Thumb Code    20  stm32f4xx_adc.o(.text)
    ADC_CommonInit                           0x080028e9   Thumb Code    34  stm32f4xx_adc.o(.text)
    ADC_CommonStructInit                     0x0800290b   Thumb Code    12  stm32f4xx_adc.o(.text)
    ADC_Cmd                                  0x08002917   Thumb Code    22  stm32f4xx_adc.o(.text)
    ADC_AnalogWatchdogCmd                    0x0800292d   Thumb Code    16  stm32f4xx_adc.o(.text)
    ADC_AnalogWatchdogThresholdsConfig       0x0800293d   Thumb Code     6  stm32f4xx_adc.o(.text)
    ADC_AnalogWatchdogSingleChannelConfig    0x08002943   Thumb Code    16  stm32f4xx_adc.o(.text)
    ADC_TempSensorVrefintCmd                 0x08002953   Thumb Code    34  stm32f4xx_adc.o(.text)
    ADC_VBATCmd                              0x08002975   Thumb Code    34  stm32f4xx_adc.o(.text)
    ADC_RegularChannelConfig                 0x08002997   Thumb Code   184  stm32f4xx_adc.o(.text)
    ADC_SoftwareStartConv                    0x08002a4f   Thumb Code    10  stm32f4xx_adc.o(.text)
    ADC_GetSoftwareStartConvStatus           0x08002a59   Thumb Code    20  stm32f4xx_adc.o(.text)
    ADC_EOCOnEachRegularChannelCmd           0x08002a6d   Thumb Code    22  stm32f4xx_adc.o(.text)
    ADC_ContinuousModeCmd                    0x08002a83   Thumb Code    22  stm32f4xx_adc.o(.text)
    ADC_DiscModeChannelCountConfig           0x08002a99   Thumb Code    24  stm32f4xx_adc.o(.text)
    ADC_DiscModeCmd                          0x08002ab1   Thumb Code    22  stm32f4xx_adc.o(.text)
    ADC_GetConversionValue                   0x08002ac7   Thumb Code     8  stm32f4xx_adc.o(.text)
    ADC_GetMultiModeConversionValue          0x08002acf   Thumb Code     8  stm32f4xx_adc.o(.text)
    ADC_DMACmd                               0x08002ad7   Thumb Code    22  stm32f4xx_adc.o(.text)
    ADC_DMARequestAfterLastTransferCmd       0x08002aed   Thumb Code    22  stm32f4xx_adc.o(.text)
    ADC_MultiModeDMARequestAfterLastTransferCmd 0x08002b03   Thumb Code    34  stm32f4xx_adc.o(.text)
    ADC_InjectedChannelConfig                0x08002b25   Thumb Code   130  stm32f4xx_adc.o(.text)
    ADC_InjectedSequencerLengthConfig        0x08002ba7   Thumb Code    24  stm32f4xx_adc.o(.text)
    ADC_SetInjectedOffset                    0x08002bbf   Thumb Code    20  stm32f4xx_adc.o(.text)
    ADC_ExternalTrigInjectedConvConfig       0x08002bd3   Thumb Code    16  stm32f4xx_adc.o(.text)
    ADC_ExternalTrigInjectedConvEdgeConfig   0x08002be3   Thumb Code    16  stm32f4xx_adc.o(.text)
    ADC_SoftwareStartInjectedConv            0x08002bf3   Thumb Code    10  stm32f4xx_adc.o(.text)
    ADC_GetSoftwareStartInjectedConvCmdStatus 0x08002bfd   Thumb Code    20  stm32f4xx_adc.o(.text)
    ADC_AutoInjectedConvCmd                  0x08002c11   Thumb Code    22  stm32f4xx_adc.o(.text)
    ADC_InjectedDiscModeCmd                  0x08002c27   Thumb Code    22  stm32f4xx_adc.o(.text)
    ADC_GetInjectedConversionValue           0x08002c3d   Thumb Code    28  stm32f4xx_adc.o(.text)
    ADC_ITConfig                             0x08002c59   Thumb Code    56  stm32f4xx_adc.o(.text)
    ADC_GetFlagStatus                        0x08002c91   Thumb Code    18  stm32f4xx_adc.o(.text)
    ADC_ClearFlag                            0x08002ca3   Thumb Code     6  stm32f4xx_adc.o(.text)
    ADC_GetITStatus                          0x08002ca9   Thumb Code    38  stm32f4xx_adc.o(.text)
    ADC_ClearITPendingBit                    0x08002ccf   Thumb Code    10  stm32f4xx_adc.o(.text)
    DAC_DeInit                               0x08002cd9   Thumb Code    22  stm32f4xx_dac.o(.text)
    DAC_Init                                 0x08002cef   Thumb Code    46  stm32f4xx_dac.o(.text)
    DAC_StructInit                           0x08002d1d   Thumb Code    12  stm32f4xx_dac.o(.text)
    DAC_Cmd                                  0x08002d29   Thumb Code    34  stm32f4xx_dac.o(.text)
    DAC_SoftwareTriggerCmd                   0x08002d4b   Thumb Code    48  stm32f4xx_dac.o(.text)
    DAC_DualSoftwareTriggerCmd               0x08002d7b   Thumb Code    38  stm32f4xx_dac.o(.text)
    DAC_WaveGenerationCmd                    0x08002da1   Thumb Code    36  stm32f4xx_dac.o(.text)
    DAC_SetChannel1Data                      0x08002dc5   Thumb Code    26  stm32f4xx_dac.o(.text)
    DAC_SetChannel2Data                      0x08002ddf   Thumb Code    26  stm32f4xx_dac.o(.text)
    DAC_SetDualChannelData                   0x08002df9   Thumb Code    32  stm32f4xx_dac.o(.text)
    DAC_GetDataOutputValue                   0x08002e19   Thumb Code    32  stm32f4xx_dac.o(.text)
    DAC_DMACmd                               0x08002e39   Thumb Code    38  stm32f4xx_dac.o(.text)
    DAC_ITConfig                             0x08002e5f   Thumb Code    36  stm32f4xx_dac.o(.text)
    DAC_GetFlagStatus                        0x08002e83   Thumb Code    28  stm32f4xx_dac.o(.text)
    DAC_ClearFlag                            0x08002e9f   Thumb Code    12  stm32f4xx_dac.o(.text)
    DAC_GetITStatus                          0x08002eab   Thumb Code    44  stm32f4xx_dac.o(.text)
    DAC_ClearITPendingBit                    0x08002ed7   Thumb Code    12  stm32f4xx_dac.o(.text)
    DMA_DeInit                               0x08002ee9   Thumb Code   324  stm32f4xx_dma.o(.text)
    DMA_Init                                 0x0800302d   Thumb Code    82  stm32f4xx_dma.o(.text)
    DMA_StructInit                           0x0800307f   Thumb Code    34  stm32f4xx_dma.o(.text)
    DMA_Cmd                                  0x080030a1   Thumb Code    22  stm32f4xx_dma.o(.text)
    DMA_PeriphIncOffsetSizeConfig            0x080030b7   Thumb Code    22  stm32f4xx_dma.o(.text)
    DMA_FlowControllerConfig                 0x080030cd   Thumb Code    22  stm32f4xx_dma.o(.text)
    DMA_SetCurrDataCounter                   0x080030e3   Thumb Code     4  stm32f4xx_dma.o(.text)
    DMA_GetCurrDataCounter                   0x080030e7   Thumb Code     8  stm32f4xx_dma.o(.text)
    DMA_DoubleBufferModeConfig               0x080030ef   Thumb Code    24  stm32f4xx_dma.o(.text)
    DMA_DoubleBufferModeCmd                  0x08003107   Thumb Code    22  stm32f4xx_dma.o(.text)
    DMA_MemoryTargetConfig                   0x0800311d   Thumb Code    10  stm32f4xx_dma.o(.text)
    DMA_GetCurrentMemoryTarget               0x08003127   Thumb Code    20  stm32f4xx_dma.o(.text)
    DMA_GetCmdStatus                         0x0800313b   Thumb Code    20  stm32f4xx_dma.o(.text)
    DMA_GetFIFOStatus                        0x0800314f   Thumb Code    12  stm32f4xx_dma.o(.text)
    DMA_GetFlagStatus                        0x0800315b   Thumb Code    56  stm32f4xx_dma.o(.text)
    DMA_ClearFlag                            0x08003193   Thumb Code    40  stm32f4xx_dma.o(.text)
    DMA_ITConfig                             0x080031bb   Thumb Code    58  stm32f4xx_dma.o(.text)
    DMA_GetITStatus                          0x080031f5   Thumb Code    84  stm32f4xx_dma.o(.text)
    DMA_ClearITPendingBit                    0x08003249   Thumb Code    40  stm32f4xx_dma.o(.text)
    GPIO_DeInit                              0x08003291   Thumb Code   268  stm32f4xx_gpio.o(.text)
    GPIO_Init                                0x0800339d   Thumb Code   144  stm32f4xx_gpio.o(.text)
    GPIO_StructInit                          0x0800342d   Thumb Code    18  stm32f4xx_gpio.o(.text)
    GPIO_PinLockConfig                       0x0800343f   Thumb Code    34  stm32f4xx_gpio.o(.text)
    GPIO_ReadInputDataBit                    0x08003461   Thumb Code    18  stm32f4xx_gpio.o(.text)
    GPIO_ReadInputData                       0x08003473   Thumb Code     8  stm32f4xx_gpio.o(.text)
    GPIO_ReadOutputDataBit                   0x0800347b   Thumb Code    18  stm32f4xx_gpio.o(.text)
    GPIO_ReadOutputData                      0x0800348d   Thumb Code     8  stm32f4xx_gpio.o(.text)
    GPIO_SetBits                             0x08003495   Thumb Code     4  stm32f4xx_gpio.o(.text)
    GPIO_ResetBits                           0x08003499   Thumb Code     4  stm32f4xx_gpio.o(.text)
    GPIO_WriteBit                            0x0800349d   Thumb Code    10  stm32f4xx_gpio.o(.text)
    GPIO_Write                               0x080034a7   Thumb Code     4  stm32f4xx_gpio.o(.text)
    GPIO_ToggleBits                          0x080034ab   Thumb Code     8  stm32f4xx_gpio.o(.text)
    GPIO_PinAFConfig                         0x080034b3   Thumb Code    70  stm32f4xx_gpio.o(.text)
    RCC_DeInit                               0x08003525   Thumb Code    82  stm32f4xx_rcc.o(.text)
    RCC_HSEConfig                            0x08003577   Thumb Code    14  stm32f4xx_rcc.o(.text)
    RCC_GetFlagStatus                        0x08003585   Thumb Code    60  stm32f4xx_rcc.o(.text)
    RCC_WaitForHSEStartUp                    0x080035c1   Thumb Code    56  stm32f4xx_rcc.o(.text)
    RCC_AdjustHSICalibrationValue            0x080035f9   Thumb Code    20  stm32f4xx_rcc.o(.text)
    RCC_HSICmd                               0x0800360d   Thumb Code     6  stm32f4xx_rcc.o(.text)
    RCC_LSEConfig                            0x08003613   Thumb Code    46  stm32f4xx_rcc.o(.text)
    RCC_LSICmd                               0x08003641   Thumb Code     6  stm32f4xx_rcc.o(.text)
    RCC_PLLConfig                            0x08003647   Thumb Code    32  stm32f4xx_rcc.o(.text)
    RCC_PLLCmd                               0x08003667   Thumb Code     6  stm32f4xx_rcc.o(.text)
    RCC_PLLI2SConfig                         0x0800366d   Thumb Code    14  stm32f4xx_rcc.o(.text)
    RCC_PLLI2SCmd                            0x0800367b   Thumb Code     6  stm32f4xx_rcc.o(.text)
    RCC_PLLSAIConfig                         0x08003681   Thumb Code    20  stm32f4xx_rcc.o(.text)
    RCC_PLLSAICmd                            0x08003695   Thumb Code     6  stm32f4xx_rcc.o(.text)
    RCC_ClockSecuritySystemCmd               0x0800369b   Thumb Code     6  stm32f4xx_rcc.o(.text)
    RCC_MCO1Config                           0x080036a1   Thumb Code    28  stm32f4xx_rcc.o(.text)
    RCC_MCO2Config                           0x080036bd   Thumb Code    28  stm32f4xx_rcc.o(.text)
    RCC_SYSCLKConfig                         0x080036d9   Thumb Code    20  stm32f4xx_rcc.o(.text)
    RCC_GetSYSCLKSource                      0x080036ed   Thumb Code    12  stm32f4xx_rcc.o(.text)
    RCC_HCLKConfig                           0x080036f9   Thumb Code    20  stm32f4xx_rcc.o(.text)
    RCC_PCLK1Config                          0x0800370d   Thumb Code    20  stm32f4xx_rcc.o(.text)
    RCC_PCLK2Config                          0x08003721   Thumb Code    22  stm32f4xx_rcc.o(.text)
    RCC_GetClocksFreq                        0x08003737   Thumb Code   222  stm32f4xx_rcc.o(.text)
    RCC_RTCCLKConfig                         0x08003815   Thumb Code    54  stm32f4xx_rcc.o(.text)
    RCC_RTCCLKCmd                            0x0800384b   Thumb Code     8  stm32f4xx_rcc.o(.text)
    RCC_BackupResetCmd                       0x08003853   Thumb Code     8  stm32f4xx_rcc.o(.text)
    RCC_I2SCLKConfig                         0x0800385b   Thumb Code     6  stm32f4xx_rcc.o(.text)
    RCC_SAIPLLI2SClkDivConfig                0x08003861   Thumb Code    26  stm32f4xx_rcc.o(.text)
    RCC_SAIPLLSAIClkDivConfig                0x0800387b   Thumb Code    28  stm32f4xx_rcc.o(.text)
    RCC_SAIBlockACLKConfig                   0x08003897   Thumb Code    20  stm32f4xx_rcc.o(.text)
    RCC_SAIBlockBCLKConfig                   0x080038ab   Thumb Code    20  stm32f4xx_rcc.o(.text)
    RCC_LTDCCLKDivConfig                     0x080038bf   Thumb Code    20  stm32f4xx_rcc.o(.text)
    RCC_TIMCLKPresConfig                     0x080038d3   Thumb Code     6  stm32f4xx_rcc.o(.text)
    RCC_AHB1PeriphClockCmd                   0x080038d9   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_AHB2PeriphClockCmd                   0x080038fb   Thumb Code    78  stm32f4xx_rcc.o(.text)
    RCC_AHB3PeriphClockCmd                   0x08003949   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_APB1PeriphClockCmd                   0x0800396b   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_APB2PeriphClockCmd                   0x0800398d   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_AHB1PeriphResetCmd                   0x080039af   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_AHB2PeriphResetCmd                   0x080039d1   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_AHB3PeriphResetCmd                   0x080039f3   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_APB1PeriphResetCmd                   0x08003a15   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_APB2PeriphResetCmd                   0x08003a37   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_AHB1PeriphClockLPModeCmd             0x08003a59   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_AHB2PeriphClockLPModeCmd             0x08003a7b   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_AHB3PeriphClockLPModeCmd             0x08003a9d   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_APB1PeriphClockLPModeCmd             0x08003abf   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_APB2PeriphClockLPModeCmd             0x08003ae1   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_LSEModeConfig                        0x08003b03   Thumb Code    40  stm32f4xx_rcc.o(.text)
    RCC_ITConfig                             0x08003b2b   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_ClearFlag                            0x08003b4d   Thumb Code    18  stm32f4xx_rcc.o(.text)
    RCC_GetITStatus                          0x08003b5f   Thumb Code    22  stm32f4xx_rcc.o(.text)
    RCC_ClearITPendingBit                    0x08003b75   Thumb Code     8  stm32f4xx_rcc.o(.text)
    TIM_DeInit                               0x08003b81   Thumb Code   346  stm32f4xx_tim.o(.text)
    TIM_TimeBaseInit                         0x08003cdb   Thumb Code   104  stm32f4xx_tim.o(.text)
    TIM_TimeBaseStructInit                   0x08003d43   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_PrescalerConfig                      0x08003d55   Thumb Code     6  stm32f4xx_tim.o(.text)
    TIM_CounterModeConfig                    0x08003d5b   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_SetCounter                           0x08003d6d   Thumb Code     4  stm32f4xx_tim.o(.text)
    TIM_SetAutoreload                        0x08003d71   Thumb Code     4  stm32f4xx_tim.o(.text)
    TIM_GetCounter                           0x08003d75   Thumb Code     6  stm32f4xx_tim.o(.text)
    TIM_GetPrescaler                         0x08003d7b   Thumb Code     6  stm32f4xx_tim.o(.text)
    TIM_UpdateDisableConfig                  0x08003d81   Thumb Code    24  stm32f4xx_tim.o(.text)
    TIM_UpdateRequestConfig                  0x08003d99   Thumb Code    24  stm32f4xx_tim.o(.text)
    TIM_ARRPreloadConfig                     0x08003db1   Thumb Code    24  stm32f4xx_tim.o(.text)
    TIM_SelectOnePulseMode                   0x08003dc9   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_SetClockDivision                     0x08003ddb   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_Cmd                                  0x08003ded   Thumb Code    24  stm32f4xx_tim.o(.text)
    TIM_OC1Init                              0x08003e05   Thumb Code   114  stm32f4xx_tim.o(.text)
    TIM_OC2Init                              0x08003e77   Thumb Code   154  stm32f4xx_tim.o(.text)
    TIM_OC3Init                              0x08003f11   Thumb Code   204  stm32f4xx_tim.o(.text)
    TIM_OC4Init                              0x08003fdd   Thumb Code   112  stm32f4xx_tim.o(.text)
    TIM_OCStructInit                         0x0800404d   Thumb Code    20  stm32f4xx_tim.o(.text)
    TIM_SelectOCxM                           0x08004061   Thumb Code    86  stm32f4xx_tim.o(.text)
    TIM_SetCompare1                          0x080040b7   Thumb Code     4  stm32f4xx_tim.o(.text)
    TIM_SetCompare2                          0x080040bb   Thumb Code     4  stm32f4xx_tim.o(.text)
    TIM_SetCompare3                          0x080040bf   Thumb Code     4  stm32f4xx_tim.o(.text)
    TIM_SetCompare4                          0x080040c3   Thumb Code     4  stm32f4xx_tim.o(.text)
    TIM_ForcedOC1Config                      0x080040c7   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_ForcedOC2Config                      0x080040d9   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_ForcedOC3Config                      0x080040f3   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_ForcedOC4Config                      0x08004105   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_OC1PreloadConfig                     0x0800411f   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_OC2PreloadConfig                     0x08004131   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_OC3PreloadConfig                     0x0800414b   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_OC4PreloadConfig                     0x0800415d   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_OC1FastConfig                        0x08004177   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_OC2FastConfig                        0x08004189   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_OC3FastConfig                        0x080041a3   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_OC4FastConfig                        0x080041b5   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_ClearOC1Ref                          0x080041cf   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_ClearOC2Ref                          0x080041e1   Thumb Code    24  stm32f4xx_tim.o(.text)
    TIM_ClearOC3Ref                          0x080041f9   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_ClearOC4Ref                          0x0800420b   Thumb Code    24  stm32f4xx_tim.o(.text)
    TIM_OC1PolarityConfig                    0x08004223   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_OC1NPolarityConfig                   0x08004235   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_OC2PolarityConfig                    0x08004247   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_OC2NPolarityConfig                   0x08004261   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_OC3PolarityConfig                    0x0800427b   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_OC3NPolarityConfig                   0x08004295   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_OC4PolarityConfig                    0x080042af   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_CCxCmd                               0x080042c9   Thumb Code    30  stm32f4xx_tim.o(.text)
    TIM_CCxNCmd                              0x080042e7   Thumb Code    30  stm32f4xx_tim.o(.text)
    TIM_SetIC4Prescaler                      0x08004305   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_SetIC3Prescaler                      0x0800436f   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_SetIC2Prescaler                      0x080043c9   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_SetIC1Prescaler                      0x0800443d   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_ICInit                               0x08004489   Thumb Code   110  stm32f4xx_tim.o(.text)
    TIM_ICStructInit                         0x080044f7   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_PWMIConfig                           0x08004509   Thumb Code   124  stm32f4xx_tim.o(.text)
    TIM_GetCapture1                          0x08004585   Thumb Code     6  stm32f4xx_tim.o(.text)
    TIM_GetCapture2                          0x0800458b   Thumb Code     6  stm32f4xx_tim.o(.text)
    TIM_GetCapture3                          0x08004591   Thumb Code     6  stm32f4xx_tim.o(.text)
    TIM_GetCapture4                          0x08004597   Thumb Code     6  stm32f4xx_tim.o(.text)
    TIM_BDTRConfig                           0x0800459d   Thumb Code    32  stm32f4xx_tim.o(.text)
    TIM_BDTRStructInit                       0x080045bd   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_CtrlPWMOutputs                       0x080045cf   Thumb Code    30  stm32f4xx_tim.o(.text)
    TIM_SelectCOM                            0x080045ed   Thumb Code    24  stm32f4xx_tim.o(.text)
    TIM_CCPreloadControl                     0x08004605   Thumb Code    24  stm32f4xx_tim.o(.text)
    TIM_ITConfig                             0x0800461d   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_GenerateEvent                        0x0800462f   Thumb Code     4  stm32f4xx_tim.o(.text)
    TIM_GetFlagStatus                        0x08004633   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_ClearFlag                            0x08004645   Thumb Code     6  stm32f4xx_tim.o(.text)
    TIM_GetITStatus                          0x0800464b   Thumb Code    34  stm32f4xx_tim.o(.text)
    TIM_ClearITPendingBit                    0x0800466d   Thumb Code     6  stm32f4xx_tim.o(.text)
    TIM_DMAConfig                            0x08004673   Thumb Code    10  stm32f4xx_tim.o(.text)
    TIM_DMACmd                               0x0800467d   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_SelectCCDMA                          0x0800468f   Thumb Code    24  stm32f4xx_tim.o(.text)
    TIM_InternalClockConfig                  0x080046a7   Thumb Code    12  stm32f4xx_tim.o(.text)
    TIM_SelectInputTrigger                   0x080046b3   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_ITRxExternalClockConfig              0x080046c5   Thumb Code    24  stm32f4xx_tim.o(.text)
    TIM_TIxExternalClockConfig               0x080046dd   Thumb Code    62  stm32f4xx_tim.o(.text)
    TIM_ETRConfig                            0x0800471b   Thumb Code    28  stm32f4xx_tim.o(.text)
    TIM_ETRClockMode1Config                  0x08004737   Thumb Code    54  stm32f4xx_tim.o(.text)
    TIM_ETRClockMode2Config                  0x0800476d   Thumb Code    32  stm32f4xx_tim.o(.text)
    TIM_SelectOutputTrigger                  0x0800478d   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_SelectSlaveMode                      0x0800479f   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_SelectMasterSlaveMode                0x080047b1   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_EncoderInterfaceConfig               0x080047c3   Thumb Code    66  stm32f4xx_tim.o(.text)
    TIM_SelectHallSensor                     0x08004805   Thumb Code    24  stm32f4xx_tim.o(.text)
    TIM_RemapConfig                          0x0800481d   Thumb Code     6  stm32f4xx_tim.o(.text)
    USART_DeInit                             0x08004825   Thumb Code   206  stm32f4xx_usart.o(.text)
    USART_Init                               0x080048f3   Thumb Code   204  stm32f4xx_usart.o(.text)
    USART_StructInit                         0x080049bf   Thumb Code    24  stm32f4xx_usart.o(.text)
    USART_ClockInit                          0x080049d7   Thumb Code    32  stm32f4xx_usart.o(.text)
    USART_ClockStructInit                    0x080049f7   Thumb Code    12  stm32f4xx_usart.o(.text)
    USART_Cmd                                0x08004a03   Thumb Code    24  stm32f4xx_usart.o(.text)
    USART_SetPrescaler                       0x08004a1b   Thumb Code    16  stm32f4xx_usart.o(.text)
    USART_OverSampling8Cmd                   0x08004a2b   Thumb Code    22  stm32f4xx_usart.o(.text)
    USART_OneBitMethodCmd                    0x08004a41   Thumb Code    24  stm32f4xx_usart.o(.text)
    USART_SendData                           0x08004a59   Thumb Code     8  stm32f4xx_usart.o(.text)
    USART_ReceiveData                        0x08004a61   Thumb Code    10  stm32f4xx_usart.o(.text)
    USART_SetAddress                         0x08004a6b   Thumb Code    18  stm32f4xx_usart.o(.text)
    USART_ReceiverWakeUpCmd                  0x08004a7d   Thumb Code    24  stm32f4xx_usart.o(.text)
    USART_WakeUpConfig                       0x08004a95   Thumb Code    18  stm32f4xx_usart.o(.text)
    USART_LINBreakDetectLengthConfig         0x08004aa7   Thumb Code    18  stm32f4xx_usart.o(.text)
    USART_LINCmd                             0x08004ab9   Thumb Code    24  stm32f4xx_usart.o(.text)
    USART_SendBreak                          0x08004ad1   Thumb Code    10  stm32f4xx_usart.o(.text)
    USART_HalfDuplexCmd                      0x08004adb   Thumb Code    24  stm32f4xx_usart.o(.text)
    USART_SetGuardTime                       0x08004af3   Thumb Code    16  stm32f4xx_usart.o(.text)
    USART_SmartCardCmd                       0x08004b03   Thumb Code    24  stm32f4xx_usart.o(.text)
    USART_SmartCardNACKCmd                   0x08004b1b   Thumb Code    24  stm32f4xx_usart.o(.text)
    USART_IrDAConfig                         0x08004b33   Thumb Code    18  stm32f4xx_usart.o(.text)
    USART_IrDACmd                            0x08004b45   Thumb Code    24  stm32f4xx_usart.o(.text)
    USART_DMACmd                             0x08004b5d   Thumb Code    18  stm32f4xx_usart.o(.text)
    USART_ITConfig                           0x08004b6f   Thumb Code    74  stm32f4xx_usart.o(.text)
    USART_GetFlagStatus                      0x08004bb9   Thumb Code    26  stm32f4xx_usart.o(.text)
    USART_ClearFlag                          0x08004bd3   Thumb Code    18  stm32f4xx_usart.o(.text)
    USART_GetITStatus                        0x08004be5   Thumb Code   118  stm32f4xx_usart.o(.text)
    USART_ClearITPendingBit                  0x08004c5b   Thumb Code    30  stm32f4xx_usart.o(.text)
    delay_init                               0x08004c79   Thumb Code    52  delay.o(.text)
    delay_us                                 0x08004cad   Thumb Code    72  delay.o(.text)
    delay_xms                                0x08004cf5   Thumb Code    72  delay.o(.text)
    delay_ms                                 0x08004d3d   Thumb Code    56  delay.o(.text)
    sys_nvic_set_vector_table                0x08004d7d   Thumb Code    12  sys.o(.text)
    sys_nvic_init                            0x08004dad   Thumb Code   116  sys.o(.text)
    sys_nvic_ex_config                       0x08004e21   Thumb Code   272  sys.o(.text)
    sys_gpio_af_set                          0x08004f31   Thumb Code   104  sys.o(.text)
    sys_gpio_set                             0x08004f99   Thumb Code   222  sys.o(.text)
    sys_gpio_pin_set                         0x08005077   Thumb Code    14  sys.o(.text)
    sys_gpio_pin_get                         0x08005085   Thumb Code    16  sys.o(.text)
    sys_wfi_set                              0x08005095   Thumb Code     4  sys.o(.text)
    sys_intx_disable                         0x08005099   Thumb Code     4  sys.o(.text)
    sys_intx_enable                          0x0800509d   Thumb Code     4  sys.o(.text)
    sys_msr_msp                              0x080050a1   Thumb Code    10  sys.o(.text)
    sys_standby                              0x080050ab   Thumb Code    72  sys.o(.text)
    sys_soft_reset                           0x080050f3   Thumb Code    12  sys.o(.text)
    sys_clock_set                            0x080050ff   Thumb Code   434  sys.o(.text)
    sys_stm32_clock_init                     0x080052b1   Thumb Code    60  sys.o(.text)
    _sys_exit                                0x080052f5   Thumb Code     4  usart.o(.text)
    fputc                                    0x080052f9   Thumb Code    22  usart.o(.text)
    uart_init                                0x0800530f   Thumb Code   164  usart.o(.text)
    USART1_IRQHandler                        0x080053b3   Thumb Code   138  usart.o(.text)
    key_config                               0x08005451   Thumb Code   506  stm32f4_key.o(.text)
    LED_Init                                 0x0800565d   Thumb Code    60  led.o(.text)
    TIM3_Int_Init                            0x0800569d   Thumb Code    48  timer.o(.text)
    TIM4_Int_Init                            0x080056cd   Thumb Code    88  timer.o(.text)
    TIM4_IRQHandler                          0x08005725   Thumb Code    60  timer.o(.text)
    TIM6_DAC_Init                            0x08005761   Thumb Code    86  timer.o(.text)
    TIM6_DAC_IRQHandler                      0x080057b7   Thumb Code    28  timer.o(.text)
    TIM5_IRQHandler                          0x080057d3   Thumb Code    28  timer.o(.text)
    Kalman_init                              0x0800580d   Thumb Code    42  kalman.o(.text)
    kalman_filter                            0x08005837   Thumb Code   154  kalman.o(.text)
    kalman                                   0x080058d1   Thumb Code    92  kalman.o(.text)
    kalman_thd                               0x0800592d   Thumb Code    62  kalman.o(.text)
    QCZ_FFT                                  0x08005991   Thumb Code   330  adc.o(.text)
    QCZ_FFT1                                 0x08005adb   Thumb Code   202  adc.o(.text)
    Adc_Init                                 0x08005ba5   Thumb Code   216  adc.o(.text)
    DMA1_Init                                0x08005c7d   Thumb Code   170  adc.o(.text)
    DMA2_Stream0_IRQHandler                  0x08005d27   Thumb Code    36  adc.o(.text)
    Adc2_Init                                0x08005d4b   Thumb Code   250  adc.o(.text)
    DMA2_Stream2_IRQHandler                  0x08005e45   Thumb Code    36  adc.o(.text)
    Adc3_Init                                0x08005e69   Thumb Code   166  adc.o(.text)
    DMA3_Init                                0x08005f0f   Thumb Code   188  adc.o(.text)
    DMA2_Stream1_IRQHandler                  0x08005fcb   Thumb Code    40  adc.o(.text)
    ADC_IRQHandler                           0x08005ff3   Thumb Code   180  adc.o(.text)
    find_peak_indices                        0x080060ed   Thumb Code   174  fft.o(.text)
    FFT                                      0x0800619b   Thumb Code   158  fft.o(.text)
    Hanningwindow                            0x08006239   Thumb Code   120  fft.o(.text)
    get_pianyik                              0x080062b1   Thumb Code   150  fft.o(.text)
    Get_basevpp_point                        0x08006347   Thumb Code    74  fft.o(.text)
    get_basefrevpp                           0x08006391   Thumb Code   274  fft.o(.text)
    Get_othervpp_point                       0x080064a3   Thumb Code    38  fft.o(.text)
    Get_vpp_fre                              0x080064c9   Thumb Code   412  fft.o(.text)
    n_get_vppfre                             0x08006665   Thumb Code   172  fft.o(.text)
    get_thd                                  0x08006711   Thumb Code   248  fft.o(.text)
    lcd_wr_data                              0x08006839   Thumb Code    18  lcd.o(.text)
    lcd_wr_regno                             0x0800684b   Thumb Code    20  lcd.o(.text)
    lcd_write_reg                            0x0800685f   Thumb Code    16  lcd.o(.text)
    lcd_write_ram_prepare                    0x0800688f   Thumb Code    12  lcd.o(.text)
    lcd_set_cursor                           0x0800689b   Thumb Code   282  lcd.o(.text)
    lcd_read_point                           0x080069b5   Thumb Code   148  lcd.o(.text)
    lcd_display_on                           0x08006a49   Thumb Code    32  lcd.o(.text)
    lcd_display_off                          0x08006a69   Thumb Code    32  lcd.o(.text)
    lcd_scan_dir                             0x08006a89   Thumb Code   578  lcd.o(.text)
    lcd_draw_point                           0x08006ccb   Thumb Code    26  lcd.o(.text)
    lcd_ssd_backlight_set                    0x08006ce5   Thumb Code    88  lcd.o(.text)
    lcd_display_dir                          0x08006d3d   Thumb Code   370  lcd.o(.text)
    lcd_set_window                           0x08006eaf   Thumb Code   370  lcd.o(.text)
    lcd_clear                                0x08007021   Thumb Code    64  lcd.o(.text)
    lcd_init                                 0x08007061   Thumb Code  1470  lcd.o(.text)
    lcd_fill                                 0x0800761f   Thumb Code    80  lcd.o(.text)
    lcd_color_fill                           0x0800766f   Thumb Code    92  lcd.o(.text)
    lcd_draw_line                            0x080076cb   Thumb Code   172  lcd.o(.text)
    lcd_draw_hline                           0x08007777   Thumb Code    52  lcd.o(.text)
    lcd_draw_rectangle                       0x080077ab   Thumb Code    74  lcd.o(.text)
    lcd_draw_circle                          0x080077f5   Thumb Code   188  lcd.o(.text)
    lcd_fill_circle                          0x080078b1   Thumb Code   176  lcd.o(.text)
    lcd_show_char                            0x08007961   Thumb Code   294  lcd.o(.text)
    lcd_show_num                             0x08007a9d   Thumb Code   152  lcd.o(.text)
    lcd_show_xnum                            0x08007b35   Thumb Code   198  lcd.o(.text)
    lcd_show_string                          0x08007bfb   Thumb Code   106  lcd.o(.text)
    lcd_ex_st7789_reginit                    0x08007c7d   Thumb Code   424  lcd_ex.o(.text)
    lcd_ex_ili9341_reginit                   0x08007e25   Thumb Code   556  lcd_ex.o(.text)
    lcd_ex_nt35310_reginit                   0x08008051   Thumb Code  3826  lcd_ex.o(.text)
    lcd_ex_st7796_reginit                    0x08008f43   Thumb Code   454  lcd_ex.o(.text)
    lcd_ex_nt35510_reginit                   0x08009109   Thumb Code  3950  lcd_ex.o(.text)
    lcd_ex_ili9806_reginit                   0x0800a077   Thumb Code   832  lcd_ex.o(.text)
    lcd_ex_ssd1963_reginit                   0x0800a3b7   Thumb Code   366  lcd_ex.o(.text)
    AD983_GPIO_Init                          0x0800a525   Thumb Code    42  ad9833.o(.text)
    AD9833_SPI_Write                         0x0800a54f   Thumb Code   186  ad9833.o(.text)
    AD9833_SetRegisterValue                  0x0800a609   Thumb Code    38  ad9833.o(.text)
    AD9833_Init                              0x0800a62f   Thumb Code    18  ad9833.o(.text)
    AD9833_Reset                             0x0800a641   Thumb Code    20  ad9833.o(.text)
    AD9833_ClearReset                        0x0800a655   Thumb Code    12  ad9833.o(.text)
    AD9833_SetFrequency                      0x0800a661   Thumb Code   116  ad9833.o(.text)
    AD9833_SetFrequencyQuick                 0x0800a6d5   Thumb Code    32  ad9833.o(.text)
    AD9833_SetPhase                          0x0800a6f5   Thumb Code    20  ad9833.o(.text)
    AD9833_Setup                             0x0800a709   Thumb Code    32  ad9833.o(.text)
    AD9833_SetWave                           0x0800a729   Thumb Code    14  ad9833.o(.text)
    AD983_GPIO_Init1                         0x0800a737   Thumb Code    42  ad9833.o(.text)
    AD9833_SetRegisterValue1                 0x0800a761   Thumb Code    14  ad9833.o(.text)
    AD9833_Init1                             0x0800a76f   Thumb Code    16  ad9833.o(.text)
    AD9833_Reset1                            0x0800a77f   Thumb Code    18  ad9833.o(.text)
    AD9833_ClearReset1                       0x0800a791   Thumb Code    10  ad9833.o(.text)
    AD9833_SetFrequency1                     0x0800a79b   Thumb Code   110  ad9833.o(.text)
    AD9833_SetFrequencyQuick1                0x0800a809   Thumb Code    32  ad9833.o(.text)
    AD9833_SetPhase1                         0x0800a829   Thumb Code    18  ad9833.o(.text)
    AD9833_Setup1                            0x0800a83b   Thumb Code    30  ad9833.o(.text)
    AD9833_SetWave1                          0x0800a859   Thumb Code    12  ad9833.o(.text)
    DAC_SetChannel1Value                     0x0800a881   Thumb Code    24  dac.o(.text)
    DAC_SetChannel1Voltage                   0x0800a899   Thumb Code    88  dac.o(.text)
    DAC_PA4_Init                             0x0800a8f1   Thumb Code    80  dac.o(.text)
    DAC_StopSineOutput                       0x0800a9d5   Thumb Code    18  dac.o(.text)
    DAC_StartSineOutput                      0x0800a9e7   Thumb Code    50  dac.o(.text)
    DAC_GetAmplitudeForFrequency             0x0800aa19   Thumb Code   212  dac.o(.text)
    DAC_SetSineFrequency                     0x0800aaed   Thumb Code   146  dac.o(.text)
    DAC_SineWave_Init                        0x0800ab7f   Thumb Code    38  dac.o(.text)
    DAC_UpdateSineOutput                     0x0800aba5   Thumb Code   100  dac.o(.text)
    DAC_SetAmplitudeMultiplier               0x0800ac09   Thumb Code    32  dac.o(.text)
    DAC_NextAmplitudeMultiplier              0x0800ac29   Thumb Code    58  dac.o(.text)
    DAC_GetAmplitudeMultiplier               0x0800ac63   Thumb Code     8  dac.o(.text)
    DAC_SetUserEnable                        0x0800ac6b   Thumb Code    22  dac.o(.text)
    DAC_GetUserEnable                        0x0800ac81   Thumb Code     6  dac.o(.text)
    DAC_Enable                               0x0800ac87   Thumb Code   102  dac.o(.text)
    DAC_Disable                              0x0800aced   Thumb Code    12  dac.o(.text)
    DAC_Test                                 0x0800acf9   Thumb Code    92  dac.o(.text)
    arm_cmplx_mag_f32                        0x0800ad59   Thumb Code   236  arm_cmplx_mag_f32.o(.text)
    arm_radix4_butterfly_f32                 0x0800ae49   Thumb Code   800  arm_cfft_radix4_f32.o(.text)
    arm_radix4_butterfly_inverse_f32         0x0800b169   Thumb Code   836  arm_cfft_radix4_f32.o(.text)
    arm_cfft_radix4_f32                      0x0800b4ad   Thumb Code    60  arm_cfft_radix4_f32.o(.text)
    arm_cfft_radix4_init_f32                 0x0800b4e9   Thumb Code   146  arm_cfft_radix4_init_f32.o(.text)
    arm_bitreversal_f32                      0x0800b5a9   Thumb Code   178  arm_bitreversal.o(.text)
    arm_bitreversal_q31                      0x0800b65b   Thumb Code   180  arm_bitreversal.o(.text)
    arm_bitreversal_q15                      0x0800b70f   Thumb Code   128  arm_bitreversal.o(.text)
    __use_no_semihosting                     0x0800b78f   Thumb Code     2  use_no_semi_2.o(.text)
    __2printf                                0x0800b791   Thumb Code    20  noretval__2printf.o(.text)
    __2sprintf                               0x0800b7a9   Thumb Code    34  noretval__2sprintf.o(.text)
    _printf_pre_padding                      0x0800b7d1   Thumb Code    44  _printf_pad.o(.text)
    _printf_post_padding                     0x0800b7fd   Thumb Code    34  _printf_pad.o(.text)
    _printf_str                              0x0800b81f   Thumb Code    82  _printf_str.o(.text)
    _printf_int_dec                          0x0800b871   Thumb Code   104  _printf_dec.o(.text)
    _printf_int_hex                          0x0800b8e9   Thumb Code    84  _printf_hex_int.o(.text)
    _printf_longlong_hex                     0x0800b8e9   Thumb Code     0  _printf_hex_int.o(.text)
    __printf                                 0x0800b941   Thumb Code   308  __printf_flags_wp.o(.text)
    strlen                                   0x0800ba79   Thumb Code    62  strlen.o(.text)
    __aeabi_memcpy4                          0x0800bab7   Thumb Code     0  rt_memcpy_w.o(.text)
    __aeabi_memcpy8                          0x0800bab7   Thumb Code     0  rt_memcpy_w.o(.text)
    __rt_memcpy_w                            0x0800bab7   Thumb Code   100  rt_memcpy_w.o(.text)
    _memcpy_lastbytes_aligned                0x0800baff   Thumb Code     0  rt_memcpy_w.o(.text)
    __use_two_region_memory                  0x0800bb1b   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow$2region                 0x0800bb1d   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand$2region                 0x0800bb1f   Thumb Code     2  heapauxi.o(.text)
    __I$use$semihosting                      0x0800bb21   Thumb Code     0  use_no_semi.o(.text)
    __use_no_semihosting_swi                 0x0800bb21   Thumb Code     2  use_no_semi.o(.text)
    __read_errno                             0x0800bb23   Thumb Code    10  _rserrno.o(.text)
    __set_errno                              0x0800bb2d   Thumb Code    12  _rserrno.o(.text)
    _printf_int_common                       0x0800bb39   Thumb Code   178  _printf_intcommon.o(.text)
    __lib_sel_fp_printf                      0x0800bbeb   Thumb Code     2  _printf_fp_dec.o(.text)
    _printf_fp_dec_real                      0x0800bd9d   Thumb Code   620  _printf_fp_dec.o(.text)
    _printf_char_common                      0x0800c013   Thumb Code    32  _printf_char_common.o(.text)
    _sputc                                   0x0800c039   Thumb Code    10  _sputc.o(.text)
    _printf_cs_common                        0x0800c043   Thumb Code    20  _printf_char.o(.text)
    _printf_char                             0x0800c057   Thumb Code    16  _printf_char.o(.text)
    _printf_string                           0x0800c067   Thumb Code     8  _printf_char.o(.text)
    _printf_char_file                        0x0800c071   Thumb Code    32  _printf_char_file.o(.text)
    __rt_locale                              0x0800c095   Thumb Code     8  rt_locale_intlibspace.o(.text)
    __aeabi_errno_addr                       0x0800c09d   Thumb Code     8  rt_errno_addr_intlibspace.o(.text)
    __errno$intlibspace                      0x0800c09d   Thumb Code     0  rt_errno_addr_intlibspace.o(.text)
    __rt_errno_addr$intlibspace              0x0800c09d   Thumb Code     0  rt_errno_addr_intlibspace.o(.text)
    _ll_udiv10                               0x0800c0a5   Thumb Code   138  lludiv10.o(.text)
    _printf_fp_infnan                        0x0800c131   Thumb Code   112  _printf_fp_infnan.o(.text)
    _btod_etento                             0x0800c1b1   Thumb Code   224  bigflt0.o(.text)
    ferror                                   0x0800c295   Thumb Code     8  ferror.o(.text)
    __user_libspace                          0x0800c29d   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x0800c29d   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x0800c29d   Thumb Code     0  libspace.o(.text)
    __user_setup_stackheap                   0x0800c2a5   Thumb Code    74  sys_stackheap_outer.o(.text)
    exit                                     0x0800c2ef   Thumb Code    18  exit.o(.text)
    strcmp                                   0x0800c301   Thumb Code   128  strcmpv7m.o(.text)
    _btod_d2e                                0x0800c381   Thumb Code    62  btod.o(CL$$btod_d2e)
    _d2e_denorm_low                          0x0800c3bf   Thumb Code    70  btod.o(CL$$btod_d2e_denorm_low)
    _d2e_norm_op1                            0x0800c405   Thumb Code    96  btod.o(CL$$btod_d2e_norm_op1)
    __btod_div_common                        0x0800c465   Thumb Code   696  btod.o(CL$$btod_div_common)
    _e2e                                     0x0800c79d   Thumb Code   220  btod.o(CL$$btod_e2e)
    _btod_ediv                               0x0800c879   Thumb Code    42  btod.o(CL$$btod_ediv)
    _btod_emul                               0x0800c8a3   Thumb Code    42  btod.o(CL$$btod_emul)
    __btod_mult_common                       0x0800c8cd   Thumb Code   580  btod.o(CL$$btod_mult_common)
    __ARM_fpclassify                         0x0800cb11   Thumb Code    48  fpclassify.o(i.__ARM_fpclassify)
    __ARM_fpclassifyf                        0x0800cb41   Thumb Code    38  fpclassifyf.o(i.__ARM_fpclassifyf)
    __hardfp_atan                            0x0800cb69   Thumb Code   622  atan.o(i.__hardfp_atan)
    __hardfp_atan2                           0x0800ce41   Thumb Code   448  atan2.o(i.__hardfp_atan2)
    __hardfp_atan2f                          0x0800d041   Thumb Code   594  atan2f.o(i.__hardfp_atan2f)
    __hardfp_cos                             0x0800d2f1   Thumb Code   180  cos.o(i.__hardfp_cos)
    __hardfp_sin                             0x0800d3b9   Thumb Code   180  sin.o(i.__hardfp_sin)
    __hardfp_sinf                            0x0800d481   Thumb Code   344  sinf.o(i.__hardfp_sinf)
    __hardfp_sqrt                            0x0800d611   Thumb Code   122  sqrt.o(i.__hardfp_sqrt)
    __hardfp_sqrtf                           0x0800d68b   Thumb Code    58  sqrtf.o(i.__hardfp_sqrtf)
    __ieee754_rem_pio2                       0x0800d6c9   Thumb Code   938  rred.o(i.__ieee754_rem_pio2)
    __kernel_cos                             0x0800db01   Thumb Code   322  cos_i.o(i.__kernel_cos)
    __kernel_poly                            0x0800dc71   Thumb Code   248  poly.o(i.__kernel_poly)
    __kernel_sin                             0x0800dd69   Thumb Code   280  sin_i.o(i.__kernel_sin)
    __mathlib_dbl_infnan                     0x0800de99   Thumb Code    20  dunder.o(i.__mathlib_dbl_infnan)
    __mathlib_dbl_infnan2                    0x0800dead   Thumb Code    20  dunder.o(i.__mathlib_dbl_infnan2)
    __mathlib_dbl_invalid                    0x0800dec1   Thumb Code    24  dunder.o(i.__mathlib_dbl_invalid)
    __mathlib_dbl_underflow                  0x0800dee1   Thumb Code    24  dunder.o(i.__mathlib_dbl_underflow)
    __mathlib_flt_infnan                     0x0800df01   Thumb Code     6  funder.o(i.__mathlib_flt_infnan)
    __mathlib_flt_infnan2                    0x0800df07   Thumb Code     6  funder.o(i.__mathlib_flt_infnan2)
    __mathlib_flt_invalid                    0x0800df0d   Thumb Code    10  funder.o(i.__mathlib_flt_invalid)
    __mathlib_flt_underflow                  0x0800df1d   Thumb Code    10  funder.o(i.__mathlib_flt_underflow)
    __mathlib_rredf2                         0x0800df2d   Thumb Code   316  rredf.o(i.__mathlib_rredf2)
    _is_digit                                0x0800e081   Thumb Code    14  __printf_wp.o(i._is_digit)
    atan                                     0x0800e08f   Thumb Code    16  atan.o(i.atan)
    fabs                                     0x0800e09f   Thumb Code    24  fabs.o(i.fabs)
    _get_lc_numeric                          0x0800e0b9   Thumb Code    44  lc_numeric_c.o(locale$$code)
    __aeabi_dneg                             0x0800e0e5   Thumb Code     0  basic.o(x$fpl$basic)
    _dneg                                    0x0800e0e5   Thumb Code     6  basic.o(x$fpl$basic)
    __aeabi_fneg                             0x0800e0eb   Thumb Code     0  basic.o(x$fpl$basic)
    _fneg                                    0x0800e0eb   Thumb Code     6  basic.o(x$fpl$basic)
    _dabs                                    0x0800e0f1   Thumb Code     6  basic.o(x$fpl$basic)
    _fabs                                    0x0800e0f7   Thumb Code     6  basic.o(x$fpl$basic)
    __aeabi_d2f                              0x0800e0fd   Thumb Code     0  d2f.o(x$fpl$d2f)
    _d2f                                     0x0800e0fd   Thumb Code    98  d2f.o(x$fpl$d2f)
    __aeabi_dadd                             0x0800e161   Thumb Code     0  daddsub_clz.o(x$fpl$dadd)
    _dadd                                    0x0800e161   Thumb Code   332  daddsub_clz.o(x$fpl$dadd)
    __fpl_dcmp_Inf                           0x0800e2b1   Thumb Code    24  dcmpi.o(x$fpl$dcmpinf)
    __aeabi_ddiv                             0x0800e2c9   Thumb Code     0  ddiv.o(x$fpl$ddiv)
    _ddiv                                    0x0800e2c9   Thumb Code   552  ddiv.o(x$fpl$ddiv)
    __aeabi_cdcmpeq                          0x0800e579   Thumb Code     0  deqf.o(x$fpl$deqf)
    _dcmpeq                                  0x0800e579   Thumb Code   120  deqf.o(x$fpl$deqf)
    __aeabi_d2iz                             0x0800e5f1   Thumb Code     0  dfix.o(x$fpl$dfix)
    _dfix                                    0x0800e5f1   Thumb Code    94  dfix.o(x$fpl$dfix)
    __aeabi_d2uiz                            0x0800e651   Thumb Code     0  dfixu.o(x$fpl$dfixu)
    _dfixu                                   0x0800e651   Thumb Code    90  dfixu.o(x$fpl$dfixu)
    __aeabi_i2d                              0x0800e6ab   Thumb Code     0  dflt_clz.o(x$fpl$dflt)
    _dflt                                    0x0800e6ab   Thumb Code    46  dflt_clz.o(x$fpl$dflt)
    __aeabi_ui2d                             0x0800e6d9   Thumb Code     0  dflt_clz.o(x$fpl$dfltu)
    _dfltu                                   0x0800e6d9   Thumb Code    38  dflt_clz.o(x$fpl$dfltu)
    __aeabi_dmul                             0x0800e701   Thumb Code     0  dmul.o(x$fpl$dmul)
    _dmul                                    0x0800e701   Thumb Code   332  dmul.o(x$fpl$dmul)
    __fpl_dnaninf                            0x0800e855   Thumb Code   156  dnaninf.o(x$fpl$dnaninf)
    __fpl_dretinf                            0x0800e8f1   Thumb Code    12  dretinf.o(x$fpl$dretinf)
    __aeabi_drsub                            0x0800e8fd   Thumb Code     0  daddsub_clz.o(x$fpl$drsb)
    _drsb                                    0x0800e8fd   Thumb Code    22  daddsub_clz.o(x$fpl$drsb)
    _dsqrt                                   0x0800e915   Thumb Code   404  dsqrt_umaal.o(x$fpl$dsqrt)
    __aeabi_dsub                             0x0800eaad   Thumb Code     0  daddsub_clz.o(x$fpl$dsub)
    _dsub                                    0x0800eaad   Thumb Code   464  daddsub_clz.o(x$fpl$dsub)
    __aeabi_f2d                              0x0800ec81   Thumb Code     0  f2d.o(x$fpl$f2d)
    _f2d                                     0x0800ec81   Thumb Code    86  f2d.o(x$fpl$f2d)
    __fpl_fnaninf                            0x0800ecd7   Thumb Code   140  fnaninf.o(x$fpl$fnaninf)
    _fp_init                                 0x0800ed63   Thumb Code    10  fpinit.o(x$fpl$fpinit)
    __fplib_config_fpu_vfp                   0x0800ed6b   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    __fplib_config_pureend_doubles           0x0800ed6b   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    __fpl_fretinf                            0x0800ed6d   Thumb Code    10  fretinf.o(x$fpl$fretinf)
    _printf_fp_dec                           0x0800ed77   Thumb Code     4  printf1.o(x$fpl$printf1)
    __I$use$fp                               0x0800ed7a   Number         0  usenofp.o(x$fpl$usenofp)
    asc2_1206                                0x0800ed7a   Data        1140  lcd.o(.constdata)
    asc2_1608                                0x0800f1ee   Data        1520  lcd.o(.constdata)
    asc2_2412                                0x0800f7de   Data        3420  lcd.o(.constdata)
    asc2_3216                                0x0801053a   Data        6080  lcd.o(.constdata)
    armBitRevTable                           0x08011d7c   Data        2048  arm_common_tables.o(.constdata)
    twiddleCoef_4096                         0x0801257c   Data       32768  arm_common_tables.o(.constdata)
    __mathlib_zero                           0x0801a680   Data           8  qnan.o(.constdata)
    Region$$Table$$Base                      0x0801a868   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x0801a888   Number         0  anon$$obj.o(Region$$Table)
    Separate                                 0x20000000   Data           1  main.o(.data)
    frequency_A                              0x20000004   Data           4  main.o(.data)
    frequency_B                              0x20000008   Data           4  main.o(.data)
    phase_difference_A                       0x2000000c   Data           4  main.o(.data)
    phase_difference_B                       0x20000010   Data           4  main.o(.data)
    phase_difference_A1                      0x20000014   Data           4  main.o(.data)
    phase_difference_B1                      0x20000018   Data           4  main.o(.data)
    current_output_freq_A                    0x20000020   Data           8  main.o(.data)
    current_output_freq_B                    0x20000028   Data           8  main.o(.data)
    phase_A_CS                               0x20000030   Data           4  main.o(.data)
    phase_B_CS                               0x20000034   Data           4  main.o(.data)
    phase_A_SX                               0x20000038   Data           4  main.o(.data)
    phase_B_SX                               0x2000003c   Data           4  main.o(.data)
    current_phase_B                          0x20000040   Data           2  main.o(.data)
    peak_idx                                 0x20000044   Data           4  main.o(.data)
    QCZ                                      0x20000048   Data           1  main.o(.data)
    QCZ1                                     0x20000049   Data           1  main.o(.data)
    QCZ_Phase                                0x2000004c   Data           8  main.o(.data)
    QCZ_Phase1                               0x20000054   Data           8  main.o(.data)
    Phase                                    0x2000005c   Data           4  main.o(.data)
    ZE                                       0x20000060   Data           4  main.o(.data)
    SBP                                      0x20000064   Data           4  main.o(.data)
    waveform_A                               0x20000068   Data           2  main.o(.data)
    waveform_B                               0x2000006a   Data           2  main.o(.data)
    waveform_A_prime                         0x2000006c   Data           2  main.o(.data)
    waveform_B_prime                         0x2000006e   Data           2  main.o(.data)
    current_frequency                        0x20000070   Data           4  main.o(.data)
    key0_pressed                             0x20000074   Data           1  main.o(.data)
    key1_pressed                             0x20000075   Data           1  main.o(.data)
    frequency_changed                        0x20000076   Data           1  main.o(.data)
    dac_multiplier_changed                   0x20000077   Data           1  main.o(.data)
    dac_enable_changed                       0x20000078   Data           1  main.o(.data)
    adc_enable_changed                       0x20000079   Data           1  main.o(.data)
    adc_user_enabled                         0x2000007a   Data           1  main.o(.data)
    selected_button                          0x2000007b   Data           1  main.o(.data)
    sweep_enabled                            0x2000007c   Data           1  main.o(.data)
    sweep_current_freq                       0x20000080   Data           4  main.o(.data)
    sweep_start_freq                         0x20000084   Data           4  main.o(.data)
    sweep_end_freq                           0x20000088   Data           4  main.o(.data)
    sweep_step_freq                          0x2000008c   Data           4  main.o(.data)
    sweep_step_count                         0x20000090   Data           2  main.o(.data)
    sweep_total_steps                        0x20000092   Data           2  main.o(.data)
    sweep_direction                          0x20000094   Data           1  main.o(.data)
    sweep_completed                          0x20000095   Data           1  main.o(.data)
    adc1_sample_index                        0x20000096   Data           2  main.o(.data)
    adc1_sampling_complete                   0x20000098   Data           1  main.o(.data)
    adc2_sample_index                        0x2000009a   Data           2  main.o(.data)
    adc2_sampling_complete                   0x2000009c   Data           1  main.o(.data)
    amplitude_analysis_enabled               0x2000009d   Data           1  main.o(.data)
    adc1_amplitude                           0x200000a0   Data           4  main.o(.data)
    adc2_amplitude                           0x200000a4   Data           4  main.o(.data)
    amplitude_ratio                          0x200000a8   Data           4  main.o(.data)
    amplitude_ratio_db                       0x200000ac   Data           4  main.o(.data)
    adc1_phase                               0x200000b0   Data           4  main.o(.data)
    adc2_phase                               0x200000b4   Data           4  main.o(.data)
    phase_difference                         0x200000b8   Data           4  main.o(.data)
    buttons                                  0x200000bc   Data         140  main.o(.data)
    SystemCoreClock                          0x2000014c   Data           4  system_stm32f4xx.o(.data)
    AHBPrescTable                            0x20000150   Data          16  system_stm32f4xx.o(.data)
    Res                                      0x20000174   Data           1  usart.o(.data)
    __stdout                                 0x20000178   Data           4  usart.o(.data)
    USART_RX_STA                             0x2000017c   Data           2  usart.o(.data)
    KAISHI                                   0x20000180   Data           4  timer.o(.data)
    last                                     0x20000184   Data           1  kalman.o(.data)
    current                                  0x20000185   Data           1  kalman.o(.data)
    last1                                    0x20000186   Data           1  kalman.o(.data)
    current1                                 0x20000187   Data           1  kalman.o(.data)
    Adresult1                                0x20000188   Data           4  adc.o(.data)
    Adresult2                                0x2000018c   Data           4  adc.o(.data)
    Adresult3                                0x20000190   Data           4  adc.o(.data)
    frequency1                               0x20000194   Data           4  adc.o(.data)
    frequency2                               0x20000198   Data           4  adc.o(.data)
    frequency3                               0x2000019c   Data           4  adc.o(.data)
    phase                                    0x200001a0   Data           4  adc.o(.data)
    phase_A                                  0x200001a4   Data           4  adc.o(.data)
    phase_B                                  0x200001a8   Data           4  adc.o(.data)
    flag_ADC                                 0x200001ac   Data           1  adc.o(.data)
    flag_ADC1                                0x200001ad   Data           1  adc.o(.data)
    flag_ADC2                                0x200001ae   Data           1  adc.o(.data)
    sampfre                                  0x200001b0   Data           4  fft.o(.data)
    flag3                                    0x200001b4   Data           1  fft.o(.data)
    Adresult                                 0x200001b8   Data           4  fft.o(.data)
    thd                                      0x200001bc   Data           4  fft.o(.data)
    k                                        0x200001c0   Data           4  fft.o(.data)
    frequency                                0x200001c4   Data           4  fft.o(.data)
    u                                        0x200001c8   Data           1  fft.o(.data)
    elec                                     0x200001cc   Data           4  fft.o(.data)
    set_right                                0x200001d0   Data           4  fft.o(.data)
    set_rightk                               0x200001d4   Data           4  fft.o(.data)
    len                                      0x200001d8   Data           1  fft.o(.data)
    peak1_idx                                0x200001dc   Data           4  fft.o(.data)
    peak2_idx                                0x200001e0   Data           4  fft.o(.data)
    length                                   0x200001e4   Data           4  fft.o(.data)
    timef                                    0x200001e8   Data           2  fft.o(.data)
    g_point_color                            0x200001ec   Data           4  lcd.o(.data)
    g_back_color                             0x200001f0   Data           4  lcd.o(.data)
    dac_output_frequency                     0x200001f4   Data           4  dac.o(.data)
    dac_output_enabled                       0x200001f8   Data           1  dac.o(.data)
    dac_user_enabled                         0x200001f9   Data           1  dac.o(.data)
    dac_amplitude_multiplier                 0x200001fc   Data           4  dac.o(.data)
    lcd_buffer                               0x20000210   Data          50  main.o(.bss)
    adc1_sample_buffer                       0x20000242   Data        8192  main.o(.bss)
    adc2_sample_buffer                       0x20002242   Data        8192  main.o(.bss)
    USART_RX_BUF                             0x20008244   Data         200  usart.o(.bss)
    state                                    0x2000830c   Data         280  kalman.o(.bss)
    state1                                   0x20008424   Data          28  kalman.o(.bss)
    buff_adc                                 0x20008440   Data        8192  adc.o(.bss)
    buff_adc2                                0x2000a440   Data        8192  adc.o(.bss)
    buff_adc3                                0x2000c440   Data        8192  adc.o(.bss)
    scfft                                    0x2000e440   Data          20  fft.o(.bss)
    fft_inputbuf                             0x2000e454   Data       32768  fft.o(.bss)
    fft_outputbuf                            0x20016454   Data       16384  fft.o(.bss)
    Vpp_buff                                 0x2001a454   Data          20  fft.o(.bss)
    fre                                      0x2001a468   Data          20  fft.o(.bss)
    ele                                      0x2001a47c   Data          20  fft.o(.bss)
    all_vpp_fre                              0x2001a490   Data          80  fft.o(.bss)
    effective_value                          0x2001a4e0   Data          24  fft.o(.bss)
    n                                        0x2001a4f8   Data        8172  fft.o(.bss)
    m                                        0x2001c4e4   Data        8172  fft.o(.bss)
    lcddev                                   0x2001e4d0   Data          14  lcd.o(.bss)
    __libspace_start                         0x2001e6e0   Data          96  libspace.o(.bss)
    __temporary_stack_top$libspace           0x2001e740   Data           0  libspace.o(.bss)



==============================================================================

Memory Map of the image

  Image Entry point : 0x08000189

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x0001aab4, Max: 0x00100000, ABSOLUTE, COMPRESSED[0x0001a93c])

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x0001a8a4, Max: 0x00100000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x00000188   Data   RO          354    RESET               startup_stm32f40_41xxx.o
    0x08000188   0x08000188   0x00000008   Code   RO         1822  * !!!main             c_w.l(__main.o)
    0x08000190   0x08000190   0x00000034   Code   RO         2255    !!!scatter          c_w.l(__scatter.o)
    0x080001c4   0x080001c4   0x0000005a   Code   RO         2253    !!dczerorl2         c_w.l(__dczerorl2.o)
    0x0800021e   0x0800021e   0x00000002   PAD
    0x08000220   0x08000220   0x0000001c   Code   RO         2257    !!handler_zi        c_w.l(__scatter_zi.o)
    0x0800023c   0x0800023c   0x00000000   Code   RO         1815    .ARM.Collect$$_printf_percent$$00000000  c_w.l(_printf_percent.o)
    0x0800023c   0x0800023c   0x00000006   Code   RO         1814    .ARM.Collect$$_printf_percent$$00000003  c_w.l(_printf_f.o)
    0x08000242   0x08000242   0x00000006   Code   RO         1813    .ARM.Collect$$_printf_percent$$00000009  c_w.l(_printf_d.o)
    0x08000248   0x08000248   0x00000006   Code   RO         1812    .ARM.Collect$$_printf_percent$$0000000C  c_w.l(_printf_x.o)
    0x0800024e   0x0800024e   0x00000006   Code   RO         1811    .ARM.Collect$$_printf_percent$$00000014  c_w.l(_printf_s.o)
    0x08000254   0x08000254   0x00000004   Code   RO         1953    .ARM.Collect$$_printf_percent$$00000017  c_w.l(_printf_percent_end.o)
    0x08000258   0x08000258   0x00000002   Code   RO         2128    .ARM.Collect$$libinit$$00000000  c_w.l(libinit.o)
    0x0800025a   0x0800025a   0x00000004   Code   RO         2129    .ARM.Collect$$libinit$$00000001  c_w.l(libinit2.o)
    0x0800025e   0x0800025e   0x00000000   Code   RO         2132    .ARM.Collect$$libinit$$00000004  c_w.l(libinit2.o)
    0x0800025e   0x0800025e   0x00000000   Code   RO         2135    .ARM.Collect$$libinit$$0000000A  c_w.l(libinit2.o)
    0x0800025e   0x0800025e   0x00000000   Code   RO         2137    .ARM.Collect$$libinit$$0000000C  c_w.l(libinit2.o)
    0x0800025e   0x0800025e   0x00000000   Code   RO         2139    .ARM.Collect$$libinit$$0000000E  c_w.l(libinit2.o)
    0x0800025e   0x0800025e   0x00000006   Code   RO         2140    .ARM.Collect$$libinit$$0000000F  c_w.l(libinit2.o)
    0x08000264   0x08000264   0x00000000   Code   RO         2142    .ARM.Collect$$libinit$$00000011  c_w.l(libinit2.o)
    0x08000264   0x08000264   0x00000000   Code   RO         2144    .ARM.Collect$$libinit$$00000013  c_w.l(libinit2.o)
    0x08000264   0x08000264   0x00000000   Code   RO         2146    .ARM.Collect$$libinit$$00000015  c_w.l(libinit2.o)
    0x08000264   0x08000264   0x0000000a   Code   RO         2147    .ARM.Collect$$libinit$$00000016  c_w.l(libinit2.o)
    0x0800026e   0x0800026e   0x00000000   Code   RO         2148    .ARM.Collect$$libinit$$00000017  c_w.l(libinit2.o)
    0x0800026e   0x0800026e   0x00000000   Code   RO         2150    .ARM.Collect$$libinit$$00000019  c_w.l(libinit2.o)
    0x0800026e   0x0800026e   0x00000000   Code   RO         2152    .ARM.Collect$$libinit$$0000001B  c_w.l(libinit2.o)
    0x0800026e   0x0800026e   0x00000000   Code   RO         2154    .ARM.Collect$$libinit$$0000001D  c_w.l(libinit2.o)
    0x0800026e   0x0800026e   0x00000000   Code   RO         2156    .ARM.Collect$$libinit$$0000001F  c_w.l(libinit2.o)
    0x0800026e   0x0800026e   0x00000000   Code   RO         2158    .ARM.Collect$$libinit$$00000021  c_w.l(libinit2.o)
    0x0800026e   0x0800026e   0x00000000   Code   RO         2160    .ARM.Collect$$libinit$$00000023  c_w.l(libinit2.o)
    0x0800026e   0x0800026e   0x00000000   Code   RO         2162    .ARM.Collect$$libinit$$00000025  c_w.l(libinit2.o)
    0x0800026e   0x0800026e   0x00000000   Code   RO         2166    .ARM.Collect$$libinit$$0000002C  c_w.l(libinit2.o)
    0x0800026e   0x0800026e   0x00000000   Code   RO         2168    .ARM.Collect$$libinit$$0000002E  c_w.l(libinit2.o)
    0x0800026e   0x0800026e   0x00000000   Code   RO         2170    .ARM.Collect$$libinit$$00000030  c_w.l(libinit2.o)
    0x0800026e   0x0800026e   0x00000000   Code   RO         2172    .ARM.Collect$$libinit$$00000032  c_w.l(libinit2.o)
    0x0800026e   0x0800026e   0x00000002   Code   RO         2173    .ARM.Collect$$libinit$$00000033  c_w.l(libinit2.o)
    0x08000270   0x08000270   0x00000002   Code   RO         2201    .ARM.Collect$$libshutdown$$00000000  c_w.l(libshutdown.o)
    0x08000272   0x08000272   0x00000000   Code   RO         2210    .ARM.Collect$$libshutdown$$00000002  c_w.l(libshutdown2.o)
    0x08000272   0x08000272   0x00000000   Code   RO         2212    .ARM.Collect$$libshutdown$$00000004  c_w.l(libshutdown2.o)
    0x08000272   0x08000272   0x00000000   Code   RO         2214    .ARM.Collect$$libshutdown$$00000006  c_w.l(libshutdown2.o)
    0x08000272   0x08000272   0x00000000   Code   RO         2217    .ARM.Collect$$libshutdown$$00000009  c_w.l(libshutdown2.o)
    0x08000272   0x08000272   0x00000000   Code   RO         2220    .ARM.Collect$$libshutdown$$0000000C  c_w.l(libshutdown2.o)
    0x08000272   0x08000272   0x00000000   Code   RO         2222    .ARM.Collect$$libshutdown$$0000000E  c_w.l(libshutdown2.o)
    0x08000272   0x08000272   0x00000000   Code   RO         2225    .ARM.Collect$$libshutdown$$00000011  c_w.l(libshutdown2.o)
    0x08000272   0x08000272   0x00000002   Code   RO         2226    .ARM.Collect$$libshutdown$$00000012  c_w.l(libshutdown2.o)
    0x08000274   0x08000274   0x00000000   Code   RO         1938    .ARM.Collect$$rtentry$$00000000  c_w.l(__rtentry.o)
    0x08000274   0x08000274   0x00000000   Code   RO         2040    .ARM.Collect$$rtentry$$00000002  c_w.l(__rtentry2.o)
    0x08000274   0x08000274   0x00000006   Code   RO         2052    .ARM.Collect$$rtentry$$00000004  c_w.l(__rtentry4.o)
    0x0800027a   0x0800027a   0x00000000   Code   RO         2042    .ARM.Collect$$rtentry$$00000009  c_w.l(__rtentry2.o)
    0x0800027a   0x0800027a   0x00000004   Code   RO         2043    .ARM.Collect$$rtentry$$0000000A  c_w.l(__rtentry2.o)
    0x0800027e   0x0800027e   0x00000000   Code   RO         2045    .ARM.Collect$$rtentry$$0000000C  c_w.l(__rtentry2.o)
    0x0800027e   0x0800027e   0x00000008   Code   RO         2046    .ARM.Collect$$rtentry$$0000000D  c_w.l(__rtentry2.o)
    0x08000286   0x08000286   0x00000002   Code   RO         2174    .ARM.Collect$$rtexit$$00000000  c_w.l(rtexit.o)
    0x08000288   0x08000288   0x00000000   Code   RO         2181    .ARM.Collect$$rtexit$$00000002  c_w.l(rtexit2.o)
    0x08000288   0x08000288   0x00000004   Code   RO         2182    .ARM.Collect$$rtexit$$00000003  c_w.l(rtexit2.o)
    0x0800028c   0x0800028c   0x00000006   Code   RO         2183    .ARM.Collect$$rtexit$$00000004  c_w.l(rtexit2.o)
    0x08000292   0x08000292   0x00000002   PAD
    0x08000294   0x08000294   0x00002294   Code   RO            3    .text               main.o
    0x08002528   0x08002528   0x0000001a   Code   RO          204    .text               stm32f4xx_it.o
    0x08002542   0x08002542   0x00000002   PAD
    0x08002544   0x08002544   0x00000210   Code   RO          328    .text               system_stm32f4xx.o
    0x08002754   0x08002754   0x00000040   Code   RO          355    .text               startup_stm32f40_41xxx.o
    0x08002794   0x08002794   0x000000e0   Code   RO          361    .text               misc.o
    0x08002874   0x08002874   0x00000464   Code   RO          407    .text               stm32f4xx_adc.o
    0x08002cd8   0x08002cd8   0x00000210   Code   RO          547    .text               stm32f4xx_dac.o
    0x08002ee8   0x08002ee8   0x000003a8   Code   RO          630    .text               stm32f4xx_dma.o
    0x08003290   0x08003290   0x00000294   Code   RO          736    .text               stm32f4xx_gpio.o
    0x08003524   0x08003524   0x0000065c   Code   RO          899    .text               stm32f4xx_rcc.o
    0x08003b80   0x08003b80   0x00000ca2   Code   RO         1044    .text               stm32f4xx_tim.o
    0x08004822   0x08004822   0x00000002   PAD
    0x08004824   0x08004824   0x00000454   Code   RO         1064    .text               stm32f4xx_usart.o
    0x08004c78   0x08004c78   0x00000104   Code   RO         1104    .text               delay.o
    0x08004d7c   0x08004d7c   0x00000578   Code   RO         1133    .text               sys.o
    0x080052f4   0x080052f4   0x0000015c   Code   RO         1159    .text               usart.o
    0x08005450   0x08005450   0x0000020c   Code   RO         1190    .text               stm32f4_key.o
    0x0800565c   0x0800565c   0x00000040   Code   RO         1213    .text               led.o
    0x0800569c   0x0800569c   0x00000170   Code   RO         1265    .text               timer.o
    0x0800580c   0x0800580c   0x00000184   Code   RO         1315    .text               kalman.o
    0x08005990   0x08005990   0x0000075c   Code   RO         1342    .text               adc.o
    0x080060ec   0x080060ec   0x0000074c   Code   RO         1385    .text               fft.o
    0x08006838   0x08006838   0x00001444   Code   RO         1416    .text               lcd.o
    0x08007c7c   0x08007c7c   0x000028a8   Code   RO         1458    .text               lcd_ex.o
    0x0800a524   0x0800a524   0x0000035c   Code   RO         1482    .text               ad9833.o
    0x0800a880   0x0800a880   0x000004d8   Code   RO         1538    .text               dac.o
    0x0800ad58   0x0800ad58   0x000000f0   Code   RO         1592    .text               arm_cortexM4lf_math.lib(arm_cmplx_mag_f32.o)
    0x0800ae48   0x0800ae48   0x000006a0   Code   RO         1637    .text               arm_cortexM4lf_math.lib(arm_cfft_radix4_f32.o)
    0x0800b4e8   0x0800b4e8   0x000000c0   Code   RO         1657    .text               arm_cortexM4lf_math.lib(arm_cfft_radix4_init_f32.o)
    0x0800b5a8   0x0800b5a8   0x000001e6   Code   RO         1681    .text               arm_cortexM4lf_math.lib(arm_bitreversal.o)
    0x0800b78e   0x0800b78e   0x00000002   Code   RO         1752    .text               c_w.l(use_no_semi_2.o)
    0x0800b790   0x0800b790   0x00000018   Code   RO         1758    .text               c_w.l(noretval__2printf.o)
    0x0800b7a8   0x0800b7a8   0x00000028   Code   RO         1760    .text               c_w.l(noretval__2sprintf.o)
    0x0800b7d0   0x0800b7d0   0x0000004e   Code   RO         1764    .text               c_w.l(_printf_pad.o)
    0x0800b81e   0x0800b81e   0x00000052   Code   RO         1766    .text               c_w.l(_printf_str.o)
    0x0800b870   0x0800b870   0x00000078   Code   RO         1768    .text               c_w.l(_printf_dec.o)
    0x0800b8e8   0x0800b8e8   0x00000058   Code   RO         1773    .text               c_w.l(_printf_hex_int.o)
    0x0800b940   0x0800b940   0x00000138   Code   RO         1803    .text               c_w.l(__printf_flags_wp.o)
    0x0800ba78   0x0800ba78   0x0000003e   Code   RO         1816    .text               c_w.l(strlen.o)
    0x0800bab6   0x0800bab6   0x00000064   Code   RO         1818    .text               c_w.l(rt_memcpy_w.o)
    0x0800bb1a   0x0800bb1a   0x00000006   Code   RO         1820    .text               c_w.l(heapauxi.o)
    0x0800bb20   0x0800bb20   0x00000002   Code   RO         1936    .text               c_w.l(use_no_semi.o)
    0x0800bb22   0x0800bb22   0x00000016   Code   RO         1939    .text               c_w.l(_rserrno.o)
    0x0800bb38   0x0800bb38   0x000000b2   Code   RO         1941    .text               c_w.l(_printf_intcommon.o)
    0x0800bbea   0x0800bbea   0x0000041e   Code   RO         1943    .text               c_w.l(_printf_fp_dec.o)
    0x0800c008   0x0800c008   0x00000030   Code   RO         1945    .text               c_w.l(_printf_char_common.o)
    0x0800c038   0x0800c038   0x0000000a   Code   RO         1947    .text               c_w.l(_sputc.o)
    0x0800c042   0x0800c042   0x0000002c   Code   RO         1949    .text               c_w.l(_printf_char.o)
    0x0800c06e   0x0800c06e   0x00000002   PAD
    0x0800c070   0x0800c070   0x00000024   Code   RO         1951    .text               c_w.l(_printf_char_file.o)
    0x0800c094   0x0800c094   0x00000008   Code   RO         2057    .text               c_w.l(rt_locale_intlibspace.o)
    0x0800c09c   0x0800c09c   0x00000008   Code   RO         2062    .text               c_w.l(rt_errno_addr_intlibspace.o)
    0x0800c0a4   0x0800c0a4   0x0000008a   Code   RO         2064    .text               c_w.l(lludiv10.o)
    0x0800c12e   0x0800c12e   0x00000002   PAD
    0x0800c130   0x0800c130   0x00000080   Code   RO         2066    .text               c_w.l(_printf_fp_infnan.o)
    0x0800c1b0   0x0800c1b0   0x000000e4   Code   RO         2070    .text               c_w.l(bigflt0.o)
    0x0800c294   0x0800c294   0x00000008   Code   RO         2095    .text               c_w.l(ferror.o)
    0x0800c29c   0x0800c29c   0x00000008   Code   RO         2114    .text               c_w.l(libspace.o)
    0x0800c2a4   0x0800c2a4   0x0000004a   Code   RO         2117    .text               c_w.l(sys_stackheap_outer.o)
    0x0800c2ee   0x0800c2ee   0x00000012   Code   RO         2119    .text               c_w.l(exit.o)
    0x0800c300   0x0800c300   0x00000080   Code   RO         2121    .text               c_w.l(strcmpv7m.o)
    0x0800c380   0x0800c380   0x0000003e   Code   RO         2073    CL$$btod_d2e        c_w.l(btod.o)
    0x0800c3be   0x0800c3be   0x00000046   Code   RO         2075    CL$$btod_d2e_denorm_low  c_w.l(btod.o)
    0x0800c404   0x0800c404   0x00000060   Code   RO         2074    CL$$btod_d2e_norm_op1  c_w.l(btod.o)
    0x0800c464   0x0800c464   0x00000338   Code   RO         2083    CL$$btod_div_common  c_w.l(btod.o)
    0x0800c79c   0x0800c79c   0x000000dc   Code   RO         2080    CL$$btod_e2e        c_w.l(btod.o)
    0x0800c878   0x0800c878   0x0000002a   Code   RO         2077    CL$$btod_ediv       c_w.l(btod.o)
    0x0800c8a2   0x0800c8a2   0x0000002a   Code   RO         2076    CL$$btod_emul       c_w.l(btod.o)
    0x0800c8cc   0x0800c8cc   0x00000244   Code   RO         2082    CL$$btod_mult_common  c_w.l(btod.o)
    0x0800cb10   0x0800cb10   0x00000030   Code   RO         2110    i.__ARM_fpclassify  m_wm.l(fpclassify.o)
    0x0800cb40   0x0800cb40   0x00000026   Code   RO         2008    i.__ARM_fpclassifyf  m_wm.l(fpclassifyf.o)
    0x0800cb66   0x0800cb66   0x00000002   PAD
    0x0800cb68   0x0800cb68   0x000002d8   Code   RO         1971    i.__hardfp_atan     m_wm.l(atan.o)
    0x0800ce40   0x0800ce40   0x00000200   Code   RO         1852    i.__hardfp_atan2    m_wm.l(atan2.o)
    0x0800d040   0x0800d040   0x000002ac   Code   RO         1864    i.__hardfp_atan2f   m_wm.l(atan2f.o)
    0x0800d2ec   0x0800d2ec   0x00000004   PAD
    0x0800d2f0   0x0800d2f0   0x000000c8   Code   RO         1876    i.__hardfp_cos      m_wm.l(cos.o)
    0x0800d3b8   0x0800d3b8   0x000000c8   Code   RO         1888    i.__hardfp_sin      m_wm.l(sin.o)
    0x0800d480   0x0800d480   0x00000190   Code   RO         1900    i.__hardfp_sinf     m_wm.l(sinf.o)
    0x0800d610   0x0800d610   0x0000007a   Code   RO         1912    i.__hardfp_sqrt     m_wm.l(sqrt.o)
    0x0800d68a   0x0800d68a   0x0000003a   Code   RO         1924    i.__hardfp_sqrtf    m_wm.l(sqrtf.o)
    0x0800d6c4   0x0800d6c4   0x00000004   PAD
    0x0800d6c8   0x0800d6c8   0x00000438   Code   RO         2025    i.__ieee754_rem_pio2  m_wm.l(rred.o)
    0x0800db00   0x0800db00   0x00000170   Code   RO         1985    i.__kernel_cos      m_wm.l(cos_i.o)
    0x0800dc70   0x0800dc70   0x000000f8   Code   RO         2112    i.__kernel_poly     m_wm.l(poly.o)
    0x0800dd68   0x0800dd68   0x00000130   Code   RO         2033    i.__kernel_sin      m_wm.l(sin_i.o)
    0x0800de98   0x0800de98   0x00000014   Code   RO         1989    i.__mathlib_dbl_infnan  m_wm.l(dunder.o)
    0x0800deac   0x0800deac   0x00000014   Code   RO         1990    i.__mathlib_dbl_infnan2  m_wm.l(dunder.o)
    0x0800dec0   0x0800dec0   0x00000020   Code   RO         1991    i.__mathlib_dbl_invalid  m_wm.l(dunder.o)
    0x0800dee0   0x0800dee0   0x00000020   Code   RO         1994    i.__mathlib_dbl_underflow  m_wm.l(dunder.o)
    0x0800df00   0x0800df00   0x00000006   Code   RO         2011    i.__mathlib_flt_infnan  m_wm.l(funder.o)
    0x0800df06   0x0800df06   0x00000006   Code   RO         2012    i.__mathlib_flt_infnan2  m_wm.l(funder.o)
    0x0800df0c   0x0800df0c   0x00000010   Code   RO         2013    i.__mathlib_flt_invalid  m_wm.l(funder.o)
    0x0800df1c   0x0800df1c   0x00000010   Code   RO         2016    i.__mathlib_flt_underflow  m_wm.l(funder.o)
    0x0800df2c   0x0800df2c   0x00000154   Code   RO         2030    i.__mathlib_rredf2  m_wm.l(rredf.o)
    0x0800e080   0x0800e080   0x0000000e   Code   RO         1801    i._is_digit         c_w.l(__printf_wp.o)
    0x0800e08e   0x0800e08e   0x00000010   Code   RO         1973    i.atan              m_wm.l(atan.o)
    0x0800e09e   0x0800e09e   0x00000018   Code   RO         2004    i.fabs              m_wm.l(fabs.o)
    0x0800e0b6   0x0800e0b6   0x00000002   PAD
    0x0800e0b8   0x0800e0b8   0x0000002c   Code   RO         2100    locale$$code        c_w.l(lc_numeric_c.o)
    0x0800e0e4   0x0800e0e4   0x00000018   Code   RO         1954    x$fpl$basic         fz_wm.l(basic.o)
    0x0800e0fc   0x0800e0fc   0x00000062   Code   RO         1824    x$fpl$d2f           fz_wm.l(d2f.o)
    0x0800e15e   0x0800e15e   0x00000002   PAD
    0x0800e160   0x0800e160   0x00000150   Code   RO         1826    x$fpl$dadd          fz_wm.l(daddsub_clz.o)
    0x0800e2b0   0x0800e2b0   0x00000018   Code   RO         2102    x$fpl$dcmpinf       fz_wm.l(dcmpi.o)
    0x0800e2c8   0x0800e2c8   0x000002b0   Code   RO         1833    x$fpl$ddiv          fz_wm.l(ddiv.o)
    0x0800e578   0x0800e578   0x00000078   Code   RO         1956    x$fpl$deqf          fz_wm.l(deqf.o)
    0x0800e5f0   0x0800e5f0   0x0000005e   Code   RO         2104    x$fpl$dfix          fz_wm.l(dfix.o)
    0x0800e64e   0x0800e64e   0x00000002   PAD
    0x0800e650   0x0800e650   0x0000005a   Code   RO         1836    x$fpl$dfixu         fz_wm.l(dfixu.o)
    0x0800e6aa   0x0800e6aa   0x0000002e   Code   RO         1841    x$fpl$dflt          fz_wm.l(dflt_clz.o)
    0x0800e6d8   0x0800e6d8   0x00000026   Code   RO         1840    x$fpl$dfltu         fz_wm.l(dflt_clz.o)
    0x0800e6fe   0x0800e6fe   0x00000002   PAD
    0x0800e700   0x0800e700   0x00000154   Code   RO         1846    x$fpl$dmul          fz_wm.l(dmul.o)
    0x0800e854   0x0800e854   0x0000009c   Code   RO         1960    x$fpl$dnaninf       fz_wm.l(dnaninf.o)
    0x0800e8f0   0x0800e8f0   0x0000000c   Code   RO         1962    x$fpl$dretinf       fz_wm.l(dretinf.o)
    0x0800e8fc   0x0800e8fc   0x00000016   Code   RO         1827    x$fpl$drsb          fz_wm.l(daddsub_clz.o)
    0x0800e912   0x0800e912   0x00000002   PAD
    0x0800e914   0x0800e914   0x00000198   Code   RO         1964    x$fpl$dsqrt         fz_wm.l(dsqrt_umaal.o)
    0x0800eaac   0x0800eaac   0x000001d4   Code   RO         1828    x$fpl$dsub          fz_wm.l(daddsub_clz.o)
    0x0800ec80   0x0800ec80   0x00000056   Code   RO         1848    x$fpl$f2d           fz_wm.l(f2d.o)
    0x0800ecd6   0x0800ecd6   0x0000008c   Code   RO         1966    x$fpl$fnaninf       fz_wm.l(fnaninf.o)
    0x0800ed62   0x0800ed62   0x0000000a   Code   RO         2178    x$fpl$fpinit        fz_wm.l(fpinit.o)
    0x0800ed6c   0x0800ed6c   0x0000000a   Code   RO         1968    x$fpl$fretinf       fz_wm.l(fretinf.o)
    0x0800ed76   0x0800ed76   0x00000004   Code   RO         1850    x$fpl$printf1       fz_wm.l(printf1.o)
    0x0800ed7a   0x0800ed7a   0x00000000   Code   RO         1970    x$fpl$usenofp       fz_wm.l(usenofp.o)
    0x0800ed7a   0x0800ed7a   0x00002f80   Data   RO         1418    .constdata          lcd.o
    0x08011cfa   0x08011cfa   0x00000002   PAD
    0x08011cfc   0x08011cfc   0x00000005   Data   RO         1483    .constdata          ad9833.o
    0x08011d01   0x08011d01   0x00000003   PAD
    0x08011d04   0x08011d04   0x00000078   Data   RO         1540    .constdata          dac.o
    0x08011d7c   0x08011d7c   0x00000800   Data   RO         1701    .constdata          arm_cortexM4lf_math.lib(arm_common_tables.o)
    0x0801257c   0x0801257c   0x00008000   Data   RO         1710    .constdata          arm_cortexM4lf_math.lib(arm_common_tables.o)
    0x0801a57c   0x0801a57c   0x00000028   Data   RO         1774    .constdata          c_w.l(_printf_hex_int.o)
    0x0801a5a4   0x0801a5a4   0x00000011   Data   RO         1804    .constdata          c_w.l(__printf_flags_wp.o)
    0x0801a5b5   0x0801a5b5   0x00000003   PAD
    0x0801a5b8   0x0801a5b8   0x00000098   Data   RO         1974    .constdata          m_wm.l(atan.o)
    0x0801a650   0x0801a650   0x00000030   Data   RO         1986    .constdata          m_wm.l(cos_i.o)
    0x0801a680   0x0801a680   0x00000008   Data   RO         2024    .constdata          m_wm.l(qnan.o)
    0x0801a688   0x0801a688   0x000000c8   Data   RO         2027    .constdata          m_wm.l(rred.o)
    0x0801a750   0x0801a750   0x00000020   Data   RO         2031    .constdata          m_wm.l(rredf.o)
    0x0801a770   0x0801a770   0x00000028   Data   RO         2034    .constdata          m_wm.l(sin_i.o)
    0x0801a798   0x0801a798   0x00000094   Data   RO         2071    .constdata          c_w.l(bigflt0.o)
    0x0801a82c   0x0801a82c   0x0000003a   Data   RO            5    .conststring        main.o
    0x0801a866   0x0801a866   0x00000002   PAD
    0x0801a868   0x0801a868   0x00000020   Data   RO         2251    Region$$Table       anon$$obj.o
    0x0801a888   0x0801a888   0x0000001c   Data   RO         2099    locale$$data        c_w.l(lc_numeric_c.o)


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x0801a8a4, Size: 0x0001ed40, Max: 0x00020000, ABSOLUTE, COMPRESSED[0x00000098])

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   COMPRESSED   0x0000014c   Data   RW            6    .data               main.o
    0x2000014c   COMPRESSED   0x00000014   Data   RW          329    .data               system_stm32f4xx.o
    0x20000160   COMPRESSED   0x00000010   Data   RW          900    .data               stm32f4xx_rcc.o
    0x20000170   COMPRESSED   0x00000004   Data   RW         1105    .data               delay.o
    0x20000174   COMPRESSED   0x0000000a   Data   RW         1161    .data               usart.o
    0x2000017e   COMPRESSED   0x00000002   PAD
    0x20000180   COMPRESSED   0x00000004   Data   RW         1266    .data               timer.o
    0x20000184   COMPRESSED   0x00000004   Data   RW         1317    .data               kalman.o
    0x20000188   COMPRESSED   0x00000027   Data   RW         1344    .data               adc.o
    0x200001af   COMPRESSED   0x00000001   PAD
    0x200001b0   COMPRESSED   0x0000003c   Data   RW         1387    .data               fft.o
    0x200001ec   COMPRESSED   0x00000008   Data   RW         1419    .data               lcd.o
    0x200001f4   COMPRESSED   0x0000001c   Data   RW         1541    .data               dac.o
    0x20000210        -       0x00008034   Zero   RW            4    .bss                main.o
    0x20008244        -       0x000000c8   Zero   RW         1160    .bss                usart.o
    0x2000830c        -       0x00000134   Zero   RW         1316    .bss                kalman.o
    0x20008440        -       0x00006000   Zero   RW         1343    .bss                adc.o
    0x2000e440        -       0x00010090   Zero   RW         1386    .bss                fft.o
    0x2001e4d0        -       0x0000000e   Zero   RW         1417    .bss                lcd.o
    0x2001e4de        -       0x00000200   Zero   RW         1539    .bss                dac.o
    0x2001e6de   COMPRESSED   0x00000002   PAD
    0x2001e6e0        -       0x00000060   Zero   RW         2115    .bss                c_w.l(libspace.o)
    0x2001e740        -       0x00000200   Zero   RW          353    HEAP                startup_stm32f40_41xxx.o
    0x2001e940        -       0x00000400   Zero   RW          352    STACK               startup_stm32f40_41xxx.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       860         28          5          0          0       5221   ad9833.o
      1884        152          0         39      24576       4850   adc.o
      1240         94        120         28        512       5697   dac.o
       260          8          0          4          0       1493   delay.o
      1868        156          0         60      65680       6051   fft.o
       388         38          0          4        308       2717   kalman.o
      5188        126      12160          8         14      15125   lcd.o
     10408          0          0          0          0       7185   lcd_ex.o
        64          4          0          0          0        531   led.o
      8852       2802         58        332      32820     303224   main.o
       224         20          0          0          0     244821   misc.o
        64         26        392          0       1536        824   startup_stm32f40_41xxx.o
       524         18          0          0          0        869   stm32f4_key.o
      1124         24          0          0          0      10698   stm32f4xx_adc.o
       528          6          0          0          0       5049   stm32f4xx_dac.o
       936         32          0          0          0       6405   stm32f4xx_dma.o
       660         44          0          0          0       4169   stm32f4xx_gpio.o
        26          0          0          0          0      66410   stm32f4xx_it.o
      1628         52          0         16          0      13052   stm32f4xx_rcc.o
      3234         60          0          0          0      23024   stm32f4xx_tim.o
      1108         34          0          0          0       7892   stm32f4xx_usart.o
      1400         42          0          0          0       6647   sys.o
       528         46          0         20          0       1795   system_stm32f4xx.o
       368         30          0          4          0     245065   timer.o
       348         20          0         10        200       3306   usart.o

    ----------------------------------------------------------------------
     43716       <USER>      <GROUP>        528     125648     992120   Object Totals
         0          0         32          0          0          0   (incl. Generated)
         4          0          7          3          2          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

       486          0          0          0          0       2210   arm_bitreversal.o
      1696          0          0          0          0       4744   arm_cfft_radix4_f32.o
       192         46          0          0          0        767   arm_cfft_radix4_init_f32.o
       240          4          0          0          0      16312   arm_cmplx_mag_f32.o
         0          0      34816          0          0       2517   arm_common_tables.o
        90          0          0          0          0          0   __dczerorl2.o
         8          0          0          0          0         68   __main.o
       312          4         17          0          0         92   __printf_flags_wp.o
        14          0          0          0          0         68   __printf_wp.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        52          8          0          0          0          0   __scatter.o
        28          0          0          0          0          0   __scatter_zi.o
        44          0          0          0          0        108   _printf_char.o
        48          6          0          0          0         96   _printf_char_common.o
        36          4          0          0          0         80   _printf_char_file.o
         6          0          0          0          0          0   _printf_d.o
       120         16          0          0          0         92   _printf_dec.o
         6          0          0          0          0          0   _printf_f.o
      1054          0          0          0          0        216   _printf_fp_dec.o
       128         16          0          0          0         84   _printf_fp_infnan.o
        88          4         40          0          0         88   _printf_hex_int.o
       178          0          0          0          0         88   _printf_intcommon.o
        78          0          0          0          0        108   _printf_pad.o
         0          0          0          0          0          0   _printf_percent.o
         4          0          0          0          0          0   _printf_percent_end.o
         6          0          0          0          0          0   _printf_s.o
        82          0          0          0          0         80   _printf_str.o
         6          0          0          0          0          0   _printf_x.o
        22          0          0          0          0        100   _rserrno.o
        10          0          0          0          0         68   _sputc.o
       228          4        148          0          0         96   bigflt0.o
      1936        128          0          0          0        672   btod.o
        18          0          0          0          0         80   exit.o
         8          0          0          0          0         68   ferror.o
         6          0          0          0          0        152   heapauxi.o
        44         10         28          0          0         76   lc_numeric_c.o
         2          0          0          0          0          0   libinit.o
        22          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         2          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
       138          0          0          0          0         80   lludiv10.o
        24          4          0          0          0         84   noretval__2printf.o
        40          6          0          0          0         84   noretval__2sprintf.o
         8          4          0          0          0         68   rt_errno_addr_intlibspace.o
         8          4          0          0          0         68   rt_locale_intlibspace.o
       100          0          0          0          0         80   rt_memcpy_w.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
       128          0          0          0          0         68   strcmpv7m.o
        62          0          0          0          0         76   strlen.o
        74          0          0          0          0         80   sys_stackheap_outer.o
         2          0          0          0          0         68   use_no_semi.o
         2          0          0          0          0         68   use_no_semi_2.o
        24          0          0          0          0        164   basic.o
        98          4          0          0          0        140   d2f.o
       826         16          0          0          0        492   daddsub_clz.o
        24          0          0          0          0        116   dcmpi.o
       688        140          0          0          0        256   ddiv.o
       120          4          0          0          0        140   deqf.o
        94          4          0          0          0        140   dfix.o
        90          4          0          0          0        140   dfixu.o
        84          0          0          0          0        232   dflt_clz.o
       340         12          0          0          0        152   dmul.o
       156          4          0          0          0        140   dnaninf.o
        12          0          0          0          0        116   dretinf.o
       408         56          0          0          0        168   dsqrt_umaal.o
        86          4          0          0          0        132   f2d.o
       140          4          0          0          0        132   fnaninf.o
        10          0          0          0          0        116   fpinit.o
        10          0          0          0          0        116   fretinf.o
         4          0          0          0          0        116   printf1.o
         0          0          0          0          0          0   usenofp.o
       744        106        152          0          0        352   atan.o
       512         64          0          0          0        208   atan2.o
       684         90          0          0          0        208   atan2f.o
       200         20          0          0          0        164   cos.o
       368         46         48          0          0        200   cos_i.o
       104         16          0          0          0        496   dunder.o
        24          0          0          0          0        124   fabs.o
        48          0          0          0          0        124   fpclassify.o
        38          0          0          0          0        116   fpclassifyf.o
        44         12          0          0          0        464   funder.o
       248          0          0          0          0        152   poly.o
         0          0          8          0          0          0   qnan.o
      1080        142        200          0          0        188   rred.o
       340         24         32          0          0        160   rredf.o
       200         20          0          0          0        164   sin.o
       304         24         40          0          0        208   sin_i.o
       400         56          0          0          0        212   sinf.o
       122          0          0          0          0        148   sqrt.o
        58          0          0          0          0        136   sqrtf.o

    ----------------------------------------------------------------------
     16686       <USER>      <GROUP>          0         96      36854   Library Totals
        28          4          3          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      2614         50      34816          0          0      26550   arm_cortexM4lf_math.lib
      5312        222        233          0         96       3472   c_w.l
      3214        252          0          0          0       3008   fz_wm.l
      5518        620        480          0          0       3824   m_wm.l

    ----------------------------------------------------------------------
     16686       <USER>      <GROUP>          0         96      36854   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     60402       5010      48306        528     125744    1016150   Grand Totals
     60402       5010      48306        152     125744    1016150   ELF Image Totals (compressed)
     60402       5010      48306        152          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)               108708 ( 106.16kB)
    Total RW  Size (RW Data + ZI Data)            126272 ( 123.31kB)
    Total ROM Size (Code + RO Data + RW Data)     108860 ( 106.31kB)

==============================================================================

