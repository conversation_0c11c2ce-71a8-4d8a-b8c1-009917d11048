# ADC快速测试指南

## 修复内容总结

### 🔧 主要修复
1. **ADC模式修改**：从双重同步模式改为独立模式
2. **编码问题修复**：将中文字符替换为英文，解决编译错误
3. **RMS算法实现**：高效的直流去除和幅度计算
4. **诊断功能增强**：完整的ADC状态检查和测试

### 🔍 新增功能
- `ADC_Diagnostics()` - 完整ADC状态诊断
- `ADC_Simple_Test()` - 快速100点采样测试
- `Print_ADC_Status()` - 实时状态监控
- 自动诊断：按下ADC按钮时自动运行测试

## 快速测试步骤

### 1. 编译烧录
```bash
# 编译项目（应该没有错误）
# 烧录到STM32
```

### 2. 连接串口
- 波特率：115200
- 数据位：8
- 停止位：1
- 校验：无

### 3. 运行测试
1. **按下"ADC ON"按钮**
2. **观察串口输出**

### 4. 预期输出

#### 正常情况：
```
Running ADC diagnostics...

=== ADC Diagnostics ===
ADC1 Enabled: Yes
ADC2 Enabled: Yes
ADC1 EOC IRQ: Enabled
ADC2 EOC IRQ: Enabled
ADC1 Ext Trigger: 0x01000000
ADC2 Ext Trigger: 0x01000000
TIM3 Enabled: Yes
TIM3 Counter: 45
TIM3 ARR: 102
ADC1 Sample Index: 0, Complete: 0
ADC2 Sample Index: 0, Complete: 0
ADC1 Data Reg: 2048
ADC2 Data Reg: 2048
=======================

=== ADC Simple Test ===
ADC Test Started - Collecting 100 samples...
Progress: ADC1=25, ADC2=25
Progress: ADC1=50, ADC2=50
Progress: ADC1=75, ADC2=75
Progress: ADC1=100, ADC2=100
Test Complete: ADC1=100, ADC2=100 samples in 123ms
First 10 ADC1 values: 2048 2049 2047 2050 2046 2051 2045 2052 2044 2053
First 10 ADC2 values: 2048 2049 2047 2050 2046 2051 2045 2052 2044 2053
=======================

Sweep Start: 100 Hz to 100000 Hz, Step: 100 Hz
ADC Sampling Started
ADC Status: ADC1[0/4096] ADC2[0/4096] Complete[0,0]
```

#### 异常情况分析：

**如果ADC未使能：**
```
ADC1 Enabled: No
ADC2 Enabled: No
```
→ 检查ADC初始化函数

**如果中断未使能：**
```
ADC1 EOC IRQ: Disabled
ADC2 EOC IRQ: Disabled
```
→ 检查中断配置

**如果TIM3未运行：**
```
TIM3 Enabled: No
TIM3 Counter: 0
```
→ 检查定时器配置

**如果采样不进行：**
```
Progress: ADC1=0, ADC2=0
Progress: ADC1=0, ADC2=0
Test Complete: ADC1=0, ADC2=0 samples in 10000ms
```
→ TIM3触发或中断问题

**如果只有一个ADC采样：**
```
Test Complete: ADC1=100, ADC2=0 samples in 123ms
```
→ ADC2配置问题

## 故障排除

### 问题1：编译错误
**错误信息：** `invalid multibyte character sequence`
**解决方案：** 已修复，所有中文字符已替换为英文

### 问题2：ADC不采样
**检查项目：**
1. TIM3是否运行（Counter值是否变化）
2. ADC是否使能
3. 中断是否配置正确
4. 外部触发是否配置为TIM3_TRGO

### 问题3：采样不同步
**检查项目：**
1. 两个ADC的外部触发配置是否相同
2. 中断服务函数是否正确处理两个ADC
3. 采样缓冲区索引是否同步

### 问题4：数据异常
**检查项目：**
1. ADC参考电压是否正确（3.3V）
2. 输入信号是否在0-3.3V范围内
3. 引脚配置是否正确（PA1, PC1）

## 配置选项

在`USER/main.c`第26-30行可以调整：
```c
#define USE_RMS_METHOD 1        // 1: RMS方法, 0: FFT方法
#define HIGHPASS_ALPHA 0.95f    // 高通滤波器参数
#define OUTPUT_PHASE_INFO 0     // 是否输出相位信息
```

## 下一步

如果简单测试通过：
1. 观察完整扫频过程
2. 检查幅度比值输出
3. 使用Python脚本分析数据

如果测试失败：
1. 根据诊断信息定位问题
2. 检查硬件连接
3. 验证时钟配置

## 技术支持

提供以下信息以获得帮助：
1. 完整的串口输出
2. 具体的错误现象
3. 硬件连接图
4. 示波器截图（如果可用）
