#!/usr/bin/env python3
"""
RMS方法测试和验证脚本
用于验证数字高通滤波器和RMS计算的正确性
"""

import numpy as np
import matplotlib.pyplot as plt
from scipy import signal

def apply_highpass_filter(data, alpha=0.95):
    """
    Python实现的数字高通滤波器（与STM32代码对应）
    
    Args:
        data: 输入数据
        alpha: 滤波器参数
    
    Returns:
        filtered_data: 滤波后的数据
    """
    filtered_data = np.zeros_like(data)
    prev_input = data[0]
    prev_output = 0.0
    
    filtered_data[0] = 0.0
    
    for i in range(1, len(data)):
        current_input = data[i]
        filtered_data[i] = alpha * (prev_output + current_input - prev_input)
        prev_input = current_input
        prev_output = filtered_data[i]
    
    return filtered_data

def calculate_rms(data):
    """
    计算RMS值
    
    Args:
        data: 输入数据
    
    Returns:
        rms: RMS值
    """
    return np.sqrt(np.mean(data**2))

def generate_test_signal(freq, sample_rate, duration, amplitude=1.0, dc_offset=2048):
    """
    生成测试信号（模拟ADC采样数据）
    
    Args:
        freq: 信号频率
        sample_rate: 采样率
        duration: 持续时间
        amplitude: 信号幅度
        dc_offset: 直流偏置（模拟ADC的中点）
    
    Returns:
        t: 时间轴
        signal: 信号数据
    """
    t = np.linspace(0, duration, int(sample_rate * duration))
    signal_data = amplitude * np.sin(2 * np.pi * freq * t) + dc_offset
    
    # 模拟ADC量化
    signal_data = np.round(signal_data).astype(np.uint16)
    signal_data = np.clip(signal_data, 0, 4095)  # 12位ADC范围
    
    return t, signal_data

def test_rms_method():
    """
    测试RMS方法的有效性
    """
    # 测试参数
    sample_rate = 815534  # STM32的采样率
    duration = 4096 / sample_rate  # 4096个采样点
    test_frequencies = [100, 500, 1000, 5000, 10000]  # 测试频率
    amplitude = 500  # 信号幅度（ADC数字值）
    dc_offset = 2048  # 直流偏置
    
    print("RMS方法测试")
    print("=" * 50)
    print(f"采样率: {sample_rate} Hz")
    print(f"采样点数: 4096")
    print(f"信号幅度: {amplitude} (ADC数字值)")
    print(f"直流偏置: {dc_offset} (ADC数字值)")
    print()
    
    results = []
    
    for freq in test_frequencies:
        # 生成测试信号
        t, signal_data = generate_test_signal(freq, sample_rate, duration, amplitude, dc_offset)
        
        # 应用高通滤波器
        filtered_data = apply_highpass_filter(signal_data.astype(float))
        
        # 计算RMS
        rms_value = calculate_rms(filtered_data)
        
        # 转换为电压
        voltage_rms = (rms_value * 3.3) / 4096
        
        # 理论RMS值（正弦波的RMS = 幅度/sqrt(2)）
        theoretical_rms_digital = amplitude / np.sqrt(2)
        theoretical_rms_voltage = (theoretical_rms_digital * 3.3) / 4096
        
        # 计算误差
        error_percent = abs(rms_value - theoretical_rms_digital) / theoretical_rms_digital * 100
        
        results.append({
            'freq': freq,
            'measured_rms': rms_value,
            'theoretical_rms': theoretical_rms_digital,
            'voltage_rms': voltage_rms,
            'error_percent': error_percent
        })
        
        print(f"频率: {freq:5d} Hz")
        print(f"  测量RMS: {rms_value:8.2f}")
        print(f"  理论RMS: {theoretical_rms_digital:8.2f}")
        print(f"  电压RMS: {voltage_rms:8.4f} V")
        print(f"  误差:    {error_percent:8.2f} %")
        print()
    
    # 绘制结果
    plt.figure(figsize=(12, 8))
    
    # 子图1：RMS值对比
    plt.subplot(2, 2, 1)
    freqs = [r['freq'] for r in results]
    measured = [r['measured_rms'] for r in results]
    theoretical = [r['theoretical_rms'] for r in results]
    
    plt.semilogx(freqs, measured, 'bo-', label='测量值')
    plt.semilogx(freqs, theoretical, 'r--', label='理论值')
    plt.xlabel('频率 (Hz)')
    plt.ylabel('RMS值 (ADC数字值)')
    plt.title('RMS值对比')
    plt.legend()
    plt.grid(True)
    
    # 子图2：误差分析
    plt.subplot(2, 2, 2)
    errors = [r['error_percent'] for r in results]
    plt.semilogx(freqs, errors, 'go-')
    plt.xlabel('频率 (Hz)')
    plt.ylabel('误差 (%)')
    plt.title('测量误差')
    plt.grid(True)
    
    # 子图3：示例信号和滤波结果
    plt.subplot(2, 2, 3)
    test_freq = 1000
    t, signal_data = generate_test_signal(test_freq, sample_rate, duration, amplitude, dc_offset)
    filtered_data = apply_highpass_filter(signal_data.astype(float))
    
    # 只显示前1000个点以便观察
    n_show = 1000
    plt.plot(t[:n_show], signal_data[:n_show], 'b-', label='原始信号', alpha=0.7)
    plt.plot(t[:n_show], filtered_data[:n_show], 'r-', label='滤波后信号')
    plt.xlabel('时间 (s)')
    plt.ylabel('幅度')
    plt.title(f'信号滤波示例 ({test_freq} Hz)')
    plt.legend()
    plt.grid(True)
    
    # 子图4：频率响应
    plt.subplot(2, 2, 4)
    alpha = 0.95
    # 计算高通滤波器的频率响应
    w = np.logspace(1, 6, 1000)  # 10Hz到1MHz
    s = 1j * w / sample_rate * 2 * np.pi
    
    # 高通滤波器传递函数近似
    # H(z) = α * (1 - z^-1) / (1 - α * z^-1)
    z = np.exp(s)
    H = alpha * (1 - 1/z) / (1 - alpha / z)
    
    plt.semilogx(w, 20 * np.log10(np.abs(H)))
    plt.xlabel('频率 (Hz)')
    plt.ylabel('幅度 (dB)')
    plt.title('高通滤波器频率响应')
    plt.grid(True)
    
    plt.tight_layout()
    plt.show()
    
    return results

def test_amplitude_ratio():
    """
    测试幅度比值计算
    """
    print("\n幅度比值测试")
    print("=" * 50)
    
    # 模拟滤波器的幅频特性
    frequencies = np.logspace(2, 5, 50)  # 100Hz到100kHz
    
    # 假设一个简单的低通滤波器特性
    fc = 3000  # 截止频率3kHz
    filter_response = 1 / np.sqrt(1 + (frequencies / fc)**2)
    
    # 模拟ADC1（滤波后）和ADC2（原始）信号
    amplitude_original = 500  # 原始信号幅度
    
    for i, freq in enumerate(frequencies[:10]):  # 只显示前10个频率点
        # ADC2（原始信号）
        adc2_amplitude = amplitude_original / np.sqrt(2)  # RMS值
        
        # ADC1（滤波后信号）
        adc1_amplitude = adc2_amplitude * filter_response[i]
        
        # 计算比值
        ratio = adc1_amplitude / adc2_amplitude
        ratio_db = 20 * np.log10(ratio)
        
        print(f"频率: {freq:6.0f} Hz, 比值: {ratio:.4f}, dB: {ratio_db:6.2f}")

if __name__ == "__main__":
    # 运行测试
    results = test_rms_method()
    test_amplitude_ratio()
    
    print("\n测试完成！")
    print("如果误差在5%以内，说明RMS方法工作正常。")
