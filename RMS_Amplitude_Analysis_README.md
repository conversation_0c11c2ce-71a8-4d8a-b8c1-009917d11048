# 扫频信号幅度分析方案优化

## 问题描述
原系统使用FFT分析扫频信号的幅度比值，但扫频信号带有直流分量，影响分析精度。需要选择更合适的方案来处理这个问题。

## 解决方案

### 方案1：数字高通滤波器 + RMS计算（推荐，已实现）

**优点：**
- 计算速度快，比FFT快10倍以上
- 有效去除直流分量
- 适合实时扫频分析
- 内存占用小
- 精度高，适合幅频特性分析

**实现原理：**
1. 使用一阶数字高通滤波器去除直流分量
2. 计算滤波后信号的RMS值作为幅度
3. 计算ADC1/ADC2的幅度比值

**核心函数：**
- `Apply_HighPass_Filter()` - 数字高通滤波器
- `Calculate_RMS_Amplitude()` - RMS幅度计算
- `Calculate_Signal_Amplitude_RMS()` - 主要分析函数

### 方案2：FFT方法（保留作为备用）

**适用场景：**
- 需要相位信息时
- 需要频域分析时
- 对特定频率成分的精确分析

**核心函数：**
- `Calculate_Signal_Amplitude_Phase_FFT()` - FFT分析
- `Process_Amplitude_Data_FFT()` - FFT数据处理

## 使用方法

### 1. 编译和烧录
直接编译修改后的代码并烧录到STM32。

### 2. 运行扫频分析
1. 按下"扫频开始"按钮
2. 系统自动使用RMS方法进行幅度分析
3. 串口输出格式：`频率(Hz),幅度比值`

### 3. 数据分析
使用提供的Python脚本 `simple_amplitude_analyzer.py` 进行数据分析：

```bash
python simple_amplitude_analyzer.py
```

## 技术细节

### 数字高通滤波器参数
- 滤波器类型：一阶IIR高通滤波器
- 公式：`y[n] = α*(y[n-1] + x[n] - x[n-1])`
- α = 0.95（对应截止频率约为采样率的1%）
- 截止频率：约8kHz（采样率815kHz的1%）

### RMS计算
- 计算公式：`RMS = sqrt(Σ(x[i]²)/N)`
- 输出：信号的有效值（RMS幅度）

### 电压转换
- ADC分辨率：12位（4096）
- 参考电压：3.3V
- 转换公式：`电压 = (RMS值 * 3.3V) / 4096`

## 性能对比

| 方法 | 计算时间 | 内存占用 | 直流处理 | 相位信息 | 适用场景 |
|------|----------|----------|----------|----------|----------|
| RMS方法 | ~1ms | 16KB | 优秀 | 无 | 幅频特性分析 |
| FFT方法 | ~10ms | 32KB | 良好 | 有 | 频域分析 |

## 注意事项

1. **采样同步**：确保ADC1和ADC2同步采样
2. **信号稳定**：每个频率点等待0.5ms让信号稳定
3. **数据有效性**：检查采样完成标志
4. **除零保护**：计算比值时检查分母不为零

## 扩展功能

如果需要相位信息，可以调用FFT备用函数：
```c
// 在Process_Amplitude_Data()中替换为：
Process_Amplitude_Data_FFT();
```

这将输出格式变为：`频率(Hz),幅度比值,相位差(弧度)`
